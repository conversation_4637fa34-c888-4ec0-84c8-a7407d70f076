<template>
	<view class="dietary-container">
		<!-- 自定义导航栏 -->
		<custom-nav-bar title="个人信息" showBack></custom-nav-bar>

		<view class="form-container">
			<view class="form-item">
				<text class="item-label">姓名</text>
				<input type="text" v-model="infoForm.name" placeholder="请输入姓名" />
			</view>
			
			<view class="form-item">
				<text class="item-label">昵称</text>
				<input type="text" v-model="infoForm.wxNickname" placeholder="请输入昵称" />
			</view>

			<view class="form-item">
				<text class="item-label">年龄</text>
				<input type="number" v-model="infoForm.age" placeholder="请输入年龄" />
			</view>
			
			<view class="form-item">
				<text class="item-label">电话号码</text>
				<input type="number" v-model="infoForm.phoneNumber" :disabled="true" placeholder="请输入电话号码" />
			</view>

			<button class="submit-btn" @click="submitInfoForm">修改个人信息</button>
		</view>
	</view>
</template>
<script setup>
	import { reactive,ref,toRaw } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import { getUserInfo,updateUserInfo } from "@/config/api/user.js";
	import CustomNavBar from '@/components/custom-nav-bar/custom-nav-bar.vue';
	
	const infoForm = reactive({
	  name: '',
	  wxNickname: '',
	  age: '',
	  phoneNumber: '',
	});
	
	const submitInfoForm = async () => {
		try {
			  const params = toRaw(infoForm)
			  const response = await updateUserInfo(params);
			  uni.setStorageSync('userInfo', JSON.stringify(response));
			  uni.showToast({
				title: '修改成功',
				icon: 'success'
			  });
			  setTimeout(() => {
				uni.navigateBack();
			  }, 1000);
		} catch (error) {
				  uni.showToast({
					title: '提交失败，请稍后重试',
					icon: 'none'
				  });
				  console.error('提交过程中发生错误:', error);
		}
	}
	
	onLoad(async (options) => {
		getUserInfo({}).then(res => {
			if(res.openId) delete res.openId
			
			console.log(res)
			Object.assign(infoForm, res);
			console.log(infoForm)
		})
	});
</script>

<style lang="scss" scoped>
	.dietary-container {
		padding: 0 20rpx 20rpx;
		background-color: #f8f5f2; 
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.header {
		padding: 20rpx 0;
		margin-bottom: 30rpx;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #8b5a2b; /* 与首页标题颜色一致 */
	}

	.form-container {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		margin-top: 20rpx;
	}
	
	.form-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
	}
	
	.form-item:first-child {
		padding-top: 0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.item-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.required:after {
		content: '*';
		color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		margin-left: 5rpx;
	}

	.error-tip {
		font-size: 24rpx;
		color: #FF4D4F;
		margin-top: 8rpx;
	}
	
	.form-item input,
	.form-item textarea{
		width: 100%;
		font-size: 28rpx;
		padding: 15rpx 20rpx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
	}

	/* input, textarea {
		border: 1rpx solid #eee;
		padding: 15rpx 20rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		width: 100%;
		box-sizing: border-box;
	}

	textarea {
		height: 300rpx;
	} */

	.picker-value {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #333;
		border: 1rpx solid #eee;
		padding: 0 20rpx;
		border-radius: 8rpx;
		background-color: #fff;
	}

	.checkbox-group {
		display: flex;
		flex-wrap: wrap;
		margin: 10rpx 0;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
		margin-bottom: 20rpx;
	}

	.checkbox-item text {
		font-size: 26rpx;
		margin-left: 8rpx;
	}

	.other-input {
		margin-top: 20rpx;
		display: flex;
		align-items: center;
	}

	.other-label {
		font-size: 26rpx;
		color: #666;
		white-space: nowrap;
		margin-right: 10rpx;
	}

	.submit-btn {
		background-color: #8b5a2b !important; /* 深棕色，与首页标题颜色一致 */
		color: #fff !important;
		border-radius: 50rpx;
		font-size: 32rpx;
		margin-top: 40rpx;
		font-weight: bold;
		border: none !important;
		outline: none !important;
	}

	/* 确保按钮在各种状态下都保持正确的颜色 */
	.submit-btn::after {
		border: none !important;
	}

	.submit-btn:hover {
		background-color: #7A4F26 !important;
		color: #fff !important;
	}

	.submit-btn:active {
		background-color: #6B4421 !important;
		color: #fff !important;
	}
</style>
