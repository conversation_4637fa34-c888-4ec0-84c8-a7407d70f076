/**
 * 图片API封装
 * 用于处理图片相关的API请求和功能
 */

import { API } from '../api.js';

/**
 * 图片尺寸枚举
 */
export const IMAGE_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium', 
  LARGE: 'large'
};

/**
 * 获取图片URL
 * @param {string} imageId - 图片ID
 * @param {string} size - 图片尺寸 (small/medium/large)
 * @returns {string} 图片URL
 */
export const getImageUrl = (imageId, size = IMAGE_SIZES.MEDIUM) => {
  if (!imageId) {
    console.warn('getImageUrl: imageId is required');
    return '';
  }
  
  return API.images.getImageUrl(imageId, size);
};

/**
 * 获取小尺寸图片URL
 * @param {string} imageId - 图片ID
 * @returns {string} 小尺寸图片URL
 */
export const getSmallImageUrl = (imageId) => {
  return getImageUrl(imageId, IMAGE_SIZES.SMALL);
};

/**
 * 获取中等尺寸图片URL
 * @param {string} imageId - 图片ID
 * @returns {string} 中等尺寸图片URL
 */
export const getMediumImageUrl = (imageId) => {
  return getImageUrl(imageId, IMAGE_SIZES.MEDIUM);
};

/**
 * 获取大尺寸图片URL
 * @param {string} imageId - 图片ID
 * @returns {string} 大尺寸图片URL
 */
export const getLargeImageUrl = (imageId) => {
  return getImageUrl(imageId, IMAGE_SIZES.LARGE);
};

/**
 * 批量生成图片对象
 * @param {Array} imageIds - 图片ID数组
 * @param {Array} names - 图片名称数组（可选）
 * @returns {Array} 图片对象数组
 */
export const generateImageList = (imageIds, names = []) => {
  if (!Array.isArray(imageIds)) {
    console.warn('generateImageList: imageIds must be an array');
    return [];
  }

  return imageIds.map((imageId, index) => ({
    id: imageId,
    name: names[index] || `图片${index + 1}`,
    smallImage: getSmallImageUrl(imageId),
    mediumImage: getMediumImageUrl(imageId),
    largeImage: getLargeImageUrl(imageId)
  }));
};

/**
 * 图片预览功能
 * @param {string} currentImage - 当前图片URL
 * @param {string} largeImage - 大图URL（可选，如果不提供则使用currentImage）
 * @param {Array} allImages - 所有图片URL数组（可选，用于支持左右滑动）
 */
export const previewImage = (currentImage, largeImage = null, allImages = []) => {
  if (!currentImage) {
    console.warn('previewImage: currentImage is required');
    return;
  }

  // 如果提供了大图URL，则使用大图，否则使用当前图片
  const previewUrl = largeImage || currentImage;
  
  // 如果提供了图片数组，则使用数组，否则只预览单张图片
  const urls = allImages.length > 0 ? allImages : [previewUrl];

  uni.previewImage({
    current: previewUrl,
    urls: urls,
    success: () => {
      console.log('图片预览成功');
    },
    fail: (err) => {
      console.error('图片预览失败:', err);
      uni.showToast({
        title: '图片加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  });
};

/**
 * 预览图片列表中的某一张
 * @param {Array} imageList - 图片列表
 * @param {number} currentIndex - 当前图片索引
 * @param {string} sizeType - 预览的图片尺寸类型 (small/medium/large)
 */
export const previewImageFromList = (imageList, currentIndex = 0, sizeType = IMAGE_SIZES.LARGE) => {
  if (!Array.isArray(imageList) || imageList.length === 0) {
    console.warn('previewImageFromList: imageList must be a non-empty array');
    return;
  }

  if (currentIndex < 0 || currentIndex >= imageList.length) {
    console.warn('previewImageFromList: currentIndex is out of range');
    return;
  }

  // 根据尺寸类型获取对应的图片URL数组
  const getUrlBySizeType = (item) => {
    switch (sizeType) {
      case IMAGE_SIZES.SMALL:
        return item.smallImage;
      case IMAGE_SIZES.MEDIUM:
        return item.mediumImage;
      case IMAGE_SIZES.LARGE:
        return item.largeImage;
      default:
        return item.largeImage;
    }
  };

  const urls = imageList.map(getUrlBySizeType);
  const currentUrl = urls[currentIndex];

  previewImage(currentUrl, null, urls);
};

/**
 * 检查图片是否加载成功
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<boolean>} 是否加载成功
 */
export const checkImageLoad = (imageUrl) => {
  return new Promise((resolve) => {
    if (!imageUrl) {
      resolve(false);
      return;
    }

    // 在小程序中，可以使用 uni.getImageInfo 来检查图片是否可以加载
    uni.getImageInfo({
      src: imageUrl,
      success: () => {
        resolve(true);
      },
      fail: () => {
        resolve(false);
      }
    });
  });
};

/**
 * 获取图片信息
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<Object>} 图片信息对象
 */
export const getImageInfo = (imageUrl) => {
  return new Promise((resolve, reject) => {
    if (!imageUrl) {
      reject(new Error('imageUrl is required'));
      return;
    }

    uni.getImageInfo({
      src: imageUrl,
      success: (res) => {
        resolve({
          width: res.width,
          height: res.height,
          path: res.path,
          orientation: res.orientation,
          type: res.type
        });
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

/**
 * 默认导出对象，包含所有功能
 */
export default {
  IMAGE_SIZES,
  getImageUrl,
  getSmallImageUrl,
  getMediumImageUrl,
  getLargeImageUrl,
  generateImageList,
  previewImage,
  previewImageFromList,
  checkImageLoad,
  getImageInfo
};
