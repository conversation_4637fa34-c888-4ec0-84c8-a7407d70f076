<template>
	<view class="team-container">
		<view class="team-header">
			<text class="header-title">专业团队</text>
		</view>
		<view class="team-content">
			<view class="team-intro">
				<image class="intro-image" src="/static/logo.png" mode="aspectFill"></image>
				<view class="intro-text">
					我们拥有一支专业的月子护理团队，包括资深月嫂、专业护士、营养师、儿科医生等，为妈妈和宝宝提供全方位的专业服务。
				</view>
			</view>
			
			<view class="team-list">
				<view class="team-item" v-for="(item, index) in teamList" :key="index" @click="viewTeamMember(item)">
					<image class="member-avatar" :src="item.avatar" mode="aspectFill"></image>
					<view class="member-info">
						<view class="member-name">{{item.name}}</view>
						<view class="member-title">{{item.title}}</view>
						<view class="member-desc">{{item.desc}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 专业团队页面
	 * 展示月子中心的专业团队成员
	 */
	export default {
		data() {
			return {
				// 团队成员列表
				teamList: [
					{ 
						id: 1,
						name: '王医生', 
						title: '儿科主任医师',
						desc: '从事儿科临床工作15年，擅长新生儿护理和常见疾病诊治',
						avatar: '/static/logo.png'
					},
					{ 
						id: 2,
						name: '李护士', 
						title: '资深护理师',
						desc: '10年妇产科护理经验，专注产妇护理和新生儿护理',
						avatar: '/static/logo.png'
					},
					{ 
						id: 3,
						name: '张营养师', 
						title: '高级营养师',
						desc: '专注产后营养调理，为产妇提供科学的饮食方案',
						avatar: '/static/logo.png'
					},
					{ 
						id: 4,
						name: '刘月嫂', 
						title: '金牌月嫂',
						desc: '8年月嫂经验，精通新生儿护理、产妇护理和母乳喂养指导',
						avatar: '/static/logo.png'
					},
					{ 
						id: 5,
						name: '陈教练', 
						title: '产后恢复教练',
						desc: '专注产后形体恢复训练，帮助产妇科学恢复身材',
						avatar: '/static/logo.png'
					}
				]
			}
		},
		methods: {
			/**
			 * 查看团队成员详情
			 * @param {Object} item 成员数据
			 */
			viewTeamMember(item) {
				uni.navigateTo({
					url: `/pages/team/detail?id=${item.id}`
				});
			}
		},
		// 分享给朋友
		onShareAppMessage() {
			return getShareAppMessageConfig({
				title: '东方爱堡月子会所 - 专业团队',
				path: '/pages_business/pages/team/team'
			});
		},
		// 分享到朋友圈
		onShareTimeline() {
			return getShareTimelineConfig({
				title: '东方爱堡月子会所专业团队，为您提供贴心专业的月子服务'
			});
		}
	}
</script>

<style>
	.team-container {
		background-color: #f8f5f2;
		min-height: 100vh;
		padding-bottom: 40rpx;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}
	
	.team-header {
		background-color: #8b5a2b;
		padding: 30rpx;
		color: #fff;
		text-align: center;
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: bold;
	}
	
	.team-content {
		padding: 20rpx;
	}
	
	.team-intro {
		background-color: #fff;
		border-radius: 10rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}
	
	.intro-image {
		width: 100%;
		height: 300rpx;
	}
	
	.intro-text {
		padding: 20rpx;
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}
	
	.team-list {
		display: flex;
		flex-direction: column;
	}
	
	.team-item {
		background-color: #fff;
		border-radius: 10rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		display: flex;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}
	
	.member-avatar {
		width: 200rpx;
		height: 200rpx;
	}
	
	.member-info {
		flex: 1;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	
	.member-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
		position: relative;
		padding-bottom: 10rpx;
	}
	
	.member-name::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		width: 40rpx;
		height: 2rpx;
		background-color: #d6bc94;
	}
	
	.member-title {
		font-size: 28rpx;
		color: #8b5a2b;
		margin-bottom: 10rpx;
	}
	
	.member-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
	}
</style>