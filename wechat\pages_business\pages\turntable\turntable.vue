<template>
  <view class="container">
    <background :src="backgroundSrc" :height="1112" />
    <button-side :top="80" @tap="handleAward">我的奖品</button-side>
    <!-- 剩余次数 -->
    <view class="tip">剩余抽奖次数 <text>{{ count }}</text> 次</view>
    <!-- 大转盘 -->
    <view class="dial">
      <image class="arrow" src="/assets/background/pin_arrow.png"></image>
      <image
        class="bg"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        src="/assets/background/pins.webp"
      />
      <image class="go" @tap="handleTurn" src="/assets/background/img_dzp_go.png">
        <view class="info">
          <view class="title">GO</view>
          <view class="cost">{{ dzpAwardInfo.credit }}积分/次</view>
        </view>
      </image>
    </view>

    <!-- 奖品介绍 -->
    <view class="intro">
      <view class="title">
        <image src="/assets/icon/icon_rule_left.png" />奖品介绍
        <image src="/assets/icon/icon_rule_right.png" />
      </view>
      <scroll-view class="content" :scroll-x="true">
        <view
          class="prize"
          v-for="item in prize"
          :key="item.id"
          v-if="item.type !== 0"
        >
          <image :src="item.cover || (item.type === 3 ? '/assets/icon/award_coupon.png' : '/assets/icon/award_credit.png')" />
          <view class="level">{{ item.level }}</view>
          <view class="name">{{ item.name }}</view>
        </view>
      </scroll-view>
    </view>
    <!-- 中奖介绍 -->
    <view class="intro" style="height: 450rpx; margin-top:25rpx">
      <view class="title">
        <image src="/assets/icon/icon_rule_left.png" />中奖名单
        <image src="/assets/icon/icon_rule_right.png" />
      </view>
      <view class="table-view head">
        <view class="block" style="width: 20%;"> <text>奖品</text> </view>
        <view class="block border1" style="width: 30%;"> <text>手机号</text> </view>
        <view class="block" style="width: 50%;border: none;"> <text>中奖时间</text> </view>
      </view>
      <view class="table-content">
        <view class="scroll-list">
          <view v-for="(item, index) in jackpotList" :key="index">
            <view class="table-view dec">
              <view class="block" style="width: 20%;"> <text>{{ item.award.name }}</text> </view>
              <view class="block border1" style="width: 30%;"> <text>{{ item.user.phone }}</text> </view>
              <view class="block" style="width: 50%;"> <text>{{ item.created }}</text> </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 规则 -->
    <view class="rule">
      <view class="title">
        <image src="/assets/icon/icon_rule_left.png" />规则
        <image src="/assets/icon/icon_rule_right.png" />
      </view>
      <view class="rule-content">
        <text>{{ dzpAwardInfo.intro }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onShow, getCurrentPages } from 'vue'
import { getDialAward, dialLottery, getDialRemain, getDialConfig, getDialConfigDetail, receiveLottery, getLotteryRecent } from '../../../api/lottery'
import { getStation } from '../../../api/station'
import { TemMsgEnum } from '../../../utils/enum'

// 响应式数据
const backgroundSrc = ref('https://cnpc-js-static.oss-cn-hangzhou.aliyuncs.com/-2134522617.png')
const index = ref(0)
const show = ref(false)
const count = ref(0)
const degree = ref(0)
const duration = ref(5)
const turnable = ref(true) // 是否允许点击：防误触
const offset = ref(0)
const prize = ref([]) // 奖品列表
const jackpotList = ref([]) // 中奖列表
const userInfo = ref({}) // 用户信息
const award_log = ref({})
const showPhone = ref(false) // 是否显示获取手机号的弹窗
const dzpAwardInfo = ref({}) // 大转盘详情
const station = ref({}) // 油站详情
const tmplIds = ref([TemMsgEnum.coupon_expire]) // 模板消息
const longitude = ref(0)
const latitude = ref(0)

// 获取全局应用实例
const app = getApp()

// 页面加载
onMounted(() => {
  const options = getCurrentPages()[getCurrentPages().length - 1].options || {}

  if (options.id) {
    fetchDialConfigDetail(options.id)
  } else {
    getDialogList()
  }

  if (app.globalData.location) {
    const { longitude: lng, latitude: lat } = app.globalData.location
    longitude.value = lng
    latitude.value = lat
    fetchStation(lng, lat)
  } else {
    app.locationReadyCallback = res => {
      const { longitude: lng, latitude: lat } = res
      longitude.value = lng
      latitude.value = lat
      fetchStation(lng, lat)
    }
  }
})

// 页面显示
onShow(() => {
  if (app.globalData.userInfo) {
    userInfo.value = app.globalData.userInfo
    isCert()
  } else {
    app.userInfoReadyCallback = res => {
      userInfo.value = res
      isCert()
    }
  }
})

/**
 * 网络请求函数：获取大转盘可用列表（用于首页、正常的大转盘抽奖）
 */
const getDialogList = () => {
  // 获取大转盘配置
  getDialConfig().then(res => {
    // 判断是否有拼团活动
    if (res.data.length === 0) {
      uni.showModal({
        title: '提示',
        content: '暂无活动',
        showCancel: false,
        success: () => {
          uni.navigateBack()
        }
      })
    } else if (res.data[0].status === 2) {
      uni.showModal({
        title: '提示',
        content: '活动已结束',
        showCancel: false,
        success: () => {
          uni.navigateBack()
        }
      })
    } else {
      if (userInfo.value.new_user === 1 && res.data[0].receiver_type === 2) {
        uni.showModal({
          showCancel: false,
          title: '提示',
          content: '此活动仅限老用户参与',
          success: () => {
            uni.switchTab({
              url: '/pages/index/index',
            })
          }
        })
      }
      if (userInfo.value.new_user === 0 && res.data[0].receiver_type === 1) {
        uni.showModal({
          showCancel: false,
          title: '提示',
          content: '此活动仅限新用户参与',
          success: () => {
            uni.switchTab({
              url: '/pages/index/index',
            })
          }
        })
      }
      dzpAwardInfo.value = res.data[0]
      // 获取抽奖次数
      fetchDialRemain()
      // 获取奖品列表
      fetchDialAward()
      isCert()
      // 获取活动获奖情况
      fetchLotteryRecent()
    }
  })
}

/**
 * 网络请求函数：获取大转盘详情（用于线下扫码等活动大转盘抽奖）
 */
const fetchDialConfigDetail = (id) => {
  getDialConfigDetail(id).then(res => {
    // 判断是否有拼团活动
    if (!res.data.id) {
      uni.showModal({
        title: '提示',
        content: '暂无活动',
        showCancel: false,
        success: () => {
          uni.navigateBack()
        }
      })
    } else if (res.data.status === 2) {
      uni.showModal({
        title: '提示',
        content: '活动已结束',
        showCancel: false,
        success: () => {
          uni.navigateBack()
        }
      })
    } else {
      if (userInfo.value.new_user === 1 && res.data.receiver_type === 2) {
        uni.showModal({
          showCancel: false,
          title: '提示',
          content: '此活动仅限老用户参与',
          success: () => {
            uni.switchTab({
              url: '/pages/index/index',
            })
          }
        })
      }
      if (userInfo.value.new_user === 0 && res.data.receiver_type === 1) {
        uni.showModal({
          showCancel: false,
          title: '提示',
          content: '此活动仅限新用户参与',
          success: () => {
            uni.switchTab({
              url: '/pages/index/index',
            })
          }
        })
      }
      dzpAwardInfo.value = res.data
      // 获取抽奖次数
      fetchDialRemain()
      // 获取奖品列表
      fetchDialAward()
      // 判断大转盘是否有认证类型
      isCert()
      // 获取活动获奖情况
      fetchLotteryRecent()
    }
  })
}

/**
 * 网络请求函数：获取抽奖次数
 */
const fetchDialRemain = () => {
  getDialRemain(dzpAwardInfo.value.id).then(res => {
    count.value = res.data.count
  })
}

/**
 * 网络请求函数：获取奖品列表
 */
const fetchDialAward = () => {
  getDialAward(dzpAwardInfo.value.id).then(res => {
    const level = ['谢谢惠顾', '一等', '二等', '三等', '四等', '五等', '六等', '七等', '八等', '九等']
    res.data.sort((a, b) => a.sort - b.sort)
    const arr = res.data.map((item, index) => ({
      id: item.id,
      cover: item.type === 1 ? item.goods_image : item.coupon_cover,
      name: item.name,
      level: item.sort !== 0 ? level[item.sort] + '奖' : '',
      type: item.type,
      index
    }))
    prize.value = arr
  })
}

/**
 * 网络请求函数：获取活动获奖情况
 */
const fetchLotteryRecent = () => {
  const params = {
    count: 10,
    config_id: dzpAwardInfo.value.id
  }
  let jackpotListData = jackpotList.value
  getLotteryRecent(params).then(res => {
    // jackpotListData = res.data.concat(...res.data).concat(...res.data)
    for (let i = 0; i <= 15; i++) {
      if (jackpotListData.length > 15) break
      jackpotListData = jackpotListData.concat(...res.data)
    }
    jackpotList.value = jackpotListData
  })
}
/**
 * 网络请求函数：获取最近附近油站
 */
const fetchStation = (lng, lat) => {
  getStation(1, 2, lng, lat).then(res => {
    if (res.data.records.length > 0) {
      station.value = res.data.records[0]
    }
  }).catch(err => {
    return uni.showToast({
      title: err.message,
      icon: 'none'
    })
  })
}

// 请求转动转盘
const handleTurn = () => {
  // 防止重复点击
  if (!turnable.value) {
    return
  }

  if (count.value <= 0) {
    return uni.showToast({
      title: '抽奖已达上限，请改天再来噢',
      icon: 'none'
    })
  }

  // 判断积分是否够抽奖
  if (userInfo.value.credit < dzpAwardInfo.value.credit) {
    return uni.showToast({
      title: '积分不足，无法抽奖',
      icon: 'none'
    })
  }

  // 未绑定手机号
  if (!userInfo.value.phone) {
    showPhone.value = true
    return
  }

  turnable.value = false
  dialLottery({ config_id: dzpAwardInfo.value.id, station_id: station.value.id }).then(res => {
    // 刷新积分
    // WxNotificationCenter.postNotificationName('refreshCredit');

    award_log.value = res.data
    const prizeItem = prize.value.find(item => {
      return item.id === res.data.award_id
    })
    if (prizeItem && prizeItem.level && prizeItem.type !== 0) {
      index.value = prizeItem.index
    } else {
      index.value = prize.value.length
    }
    dialRotate()
  }).catch(() => {
    // 未中奖
    index.value = prize.value.length
    dialRotate()
  })
}

// 获取手机号成功
const successPhone = (e) => {
  userInfo.value.phone = e.detail
}

// 转盘转动
const dialRotate = () => {
  const level = prize.value.length - index.value
  // 转动圈数：5~10圈 / 2 四舍五入： 3 ~ 5圈
  const durationTime = Math.random() * 5 + 5
  // 每个奖项占用的角度
  let proportion = 360 / prize.value.length
  degree.value = Math.round(durationTime / 2) * 360 + level * proportion
  duration.value = durationTime
  count.value = count.value - 1

  setTimeout(() => {
    turnable.value = true
    show.value = true
    if (count.value) {
      degree.value = degree.value % 360
      duration.value = 0
    }
  }, durationTime * 1000)
}

// 继续抽奖
const handleContinue = () => {
  handleClose()
  handleTurn()
}

// 关闭弹窗
const handleClose = () => {
  show.value = false
  // 防止窗口还没消失，又可以点击
  setTimeout(() => {
    turnable.value = true
  }, 500)
}

// 跳转到我的奖品
const handleAward = () => {
  uni.navigateTo({
    url: '/pages/shop/prize/index?active=1'
  })
}

// 领取奖品
const handleReceive = () => {
  // 防止重复点击
  if (!turnable.value) {
    return
  }

  let awardLog = award_log.value
  if (awardLog.award_type === 1) {
    uni.navigateTo({
      url: `/pages/shop/prize/submit/index?id=${awardLog.id}&active=1`
    })
  } else {
    turnable.value = false
    // 领取奖品
    receiveLottery({
      award_log_id: awardLog.id
    }).then(res => {
      uni.showToast({
        title: '领取成功',
        icon: 'none'
      })
      const { award_type } = res.data
      if (award_type === 3 || award_type === 4) {
        // uni.requestSubscribeMessage({
        //   tmplIds: tmplIds.value
        // })
      }

      // 刷新积分
      if (res.data.award_type === 2 || res.data.award_type === 4) {
        // WxNotificationCenter.postNotificationName('refreshCredit');
      }

      handleClose()
    })
  }
}

// 判断大转盘是否有认证类型
const isCert = () => {
  if (dzpAwardInfo.value.cert_type_id) { // 判断是否配置了认证类型
    // 判断用户是否认证
    if (userInfo.value.cert_type_id) {
      // 判断用户的认证类型是否与 大转盘活动类型一致
      if (userInfo.value.cert_type_id !== dzpAwardInfo.value.cert_type_id) {
        return uni.showModal({
          title: '提示',
          content: '认证类型与大转盘活动不一致,暂无权参与!',
          showCancel: false,
          success: () => {
            uni.switchTab({
              url: '/pages/index/index',
            })
          }
        })
      }
    } else { // 跳转到认证页面
      return uni.showModal({
        title: '提示',
        content: '请认证后参与活动',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: `/pages/cert/car/index?certtypeid=${dzpAwardInfo.value.cert_type_id}`,
          })
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
/* pages/shop/dial/index.wxss */

.container {
  background: #f8ae61;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
  position: relative;
}

.dial .bg {
  width: 620rpx;
  height: 620rpx;
}

.dial .arrow {
  position: absolute;
  top: -20rpx;
  left: 280rpx;
  width: 64rpx;
  height: 100rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  top: 230rpx;
  left: 230rpx;
  width: 160rpx;
  height: 160rpx;
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}

/* intro */

.intro {
  width: 700rpx;
  height: 340rpx;
  margin-top: 128rpx;
  padding: 25rpx;
  border-radius: 6px;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
}

.intro .title {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45rpx;
  margin: 20rpx 0;
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.intro .content {
  overflow: auto;
}

.intro .title image {
  width: 64rpx;
  height: 20rpx;
}

.intro .title image:first-child {
  margin-right: 20rpx;
}

.intro .title image:last-child {
  margin-left: 20rpx;
}

.intro .content {
  width: 700rpx;
  margin-left: -25rpx;
  white-space: nowrap;
}

/* prize */

.prize {
  position: relative;
  display: inline-block;
  width: 168rpx;
}

.prize {
  margin-left: 20rpx;
}

.prize:last-child {
  margin-right: 20rpx;
}

.prize image {
  width: 168rpx;
  height: 168rpx;
  background-color: #f3f3f3;
}

.prize .level {
  position: absolute;
  top: 128rpx;
  width: 168rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, .3);
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  line-height: 40rpx;
}

.prize .name {
  height: 34rpx;
  overflow: hidden;
  color: #333;
  font-size: 24rpx;
  text-align: center;
  text-overflow: ellipsis;
  line-height: 34rpx;
  white-space: nowrap;
}

/* 规则 */
.rule {
  width: 700rpx;
  /* height: 700rpx; */
  margin: 25rpx 0;
  padding: 25rpx;
  border-radius: 6px;
  background-color: #fff;
  box-sizing: border-box;
}

.rule .title {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45rpx;
  margin: 20rpx 0;
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.rule .title image {
  width: 64rpx;
  height: 20rpx;
}

.rule .title image:first-child {
  margin-right: 20rpx;
}

.rule .title image:last-child {
  margin-left: 20rpx;
}

.rule .item {
  margin: 32rpx 16rpx;
  color: #333;
  font-size: 24rpx;
}

.rule-content {
  color: #333;
  font-size: 24rpx;
}

/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}


/* 中奖介绍 */
.table-content {
  position: relative;
  height: 240rpx;
  width: 100%;
  margin: auto;
  overflow: hidden;
}

.table-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60rpx;
  width: 100%;
}

.head {
  font-size: 35rpx;
  font-weight: 500;
  color: #fff;
  background: #7b0c08;
}

.dec{
  font-size: 28rpx;
  color: #7b0c08;
}


.table-view .block {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  
}
.border1{
  border-left: 4rpx solid #c4644a !important;
  border-right: 4rpx solid #c4644a !important;
}

.table-content>.scroll-list {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: scroll 10s linear infinite normal;
}

.table-content>.scroll-list>view {
  width: 100%;
  height: 60rpx;
  background-color: #fde4cb;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.table-content>.scroll-list>view:nth-child(2n) {
  background-color: #f4cfac;
}

@keyframes scroll {
  100% {
    /* 需要滚动内容的总高度 */
    top: -480rpx;
  }
}

</style>