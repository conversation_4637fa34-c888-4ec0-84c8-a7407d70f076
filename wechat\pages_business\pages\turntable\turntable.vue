<template>
  <view class="container">
    <!-- 大转盘 -->
    <view class="dial">
      <!-- <image class="arrow" src="/assets/background/pin_arrow.png"></image> -->
      <image
        :class="['bg', { stopping: isStopping }]"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        src="/static/images/pins.webp"
      />
      <image
        :class="['go', { spinning: isSpinning }]"
        @tap="handleTurn"
        src="/static/images/pins_go.webp"
      ></image>
    </view>


  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const degree = ref(0) // 转盘旋转角度
const duration = ref(0) // 动画持续时间
const isSpinning = ref(false) // 是否正在旋转
const isStopping = ref(false) // 是否正在停止

// 点击转盘按钮
const handleTurn = () => {
  // 如果正在旋转，点击停止
  if (isSpinning.value) {
    console.log('点击停止转盘')
    stopSpinning()
    return
  }

  console.log('开始转盘旋转')

  // 开始旋转
  isSpinning.value = true

  // 设置旋转参数
  const spinDuration = 4 // 旋转持续时间（秒）
  const totalRotation = 10 * 360 // 转10圈，每圈360度
  const randomStop = Math.random() * 360 // 随机停止位置

  // 设置动画时间和角度
  duration.value = spinDuration
  degree.value = degree.value + totalRotation + randomStop

  console.log(`转盘将旋转到: ${degree.value}度，持续时间: ${spinDuration}秒`)

  // 旋转结束后重置状态
  setTimeout(() => {
    if (isSpinning.value) { // 只有在没有被手动停止的情况下才执行
      isSpinning.value = false
      console.log('转盘旋转自然结束')
      // 重置角度到0-360范围内，保持视觉连续性
      degree.value = degree.value % 360
      duration.value = 0
    }
  }, spinDuration * 1000)
}

// 停止转盘旋转
const stopSpinning = () => {
  // 设置停止状态
  isStopping.value = true

  // 获取当前转盘的角度
  const currentDegree = degree.value % 360

  // 设置慢慢停下的动画
  const stopDuration = 2 // 停止动画持续时间（秒）
  const finalDegree = currentDegree + Math.random() * 360 + 180 // 再转半圈到一圈然后停下

  // 更新动画参数
  duration.value = stopDuration
  degree.value = degree.value + finalDegree

  console.log(`转盘慢慢停下，最终角度: ${degree.value}度`)

  // 停止动画结束后重置状态
  setTimeout(() => {
    isSpinning.value = false
    isStopping.value = false
    console.log('转盘完全停止')
    // 重置角度到0-360范围内，保持视觉连续性
    degree.value = degree.value % 360
    duration.value = 0
  }, stopDuration * 1000)
}
</script>

<style lang="scss" scoped>
/* pages/shop/dial/index.wxss */

.container {
  width: 100vw;
  height: 100vh;
  padding-top:15vh ;
  background: #f8ae61;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.dial .bg {
  width: 700rpx;
  height: 700rpx;
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1); /* 先快后慢的缓动效果 */
}

/* 转盘停止时的缓动效果 */
.dial .bg.stopping {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 更平缓的停止效果 */
}

.dial .arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%,-50%);
  width: 64rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  margin: auto;
  width: 120rpx;
  height: 140rpx;
  z-index: 100;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.dial .go:hover {
  transform: scale(1.05);
}

.dial .go:active {
  transform: scale(0.95);
}

/* 转盘旋转时按钮的样式 */
.dial .go.spinning {
  animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}



/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}




</style>