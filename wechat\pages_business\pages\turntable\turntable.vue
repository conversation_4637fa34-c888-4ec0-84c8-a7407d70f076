<template>
  <view class="container">
    <!-- 大转盘 -->
    <view class="dial">
      <!-- <image class="arrow" src="/assets/background/pin_arrow.png"></image> -->
      <image
        class="bg"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        src="/static/images/pins.webp"
      />
      <image class="go" @tap="handleTurn" src="/static/images/pins_go.webp"></image>
    </view>


  </view>
</template>

<script setup>

</script>

<style lang="scss" scoped>
/* pages/shop/dial/index.wxss */

.container {
  width: 100vw;
  height: 100vh;
  background: #f8ae61;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
  position: relative;
}

.dial .bg {
  width: 700rpx;
  height: 700rpx;
}

.dial .arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%,-50%);
  width: 64rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%,-50%);
  width: 64rpx;
  z-index: 100;
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}



/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}




</style>