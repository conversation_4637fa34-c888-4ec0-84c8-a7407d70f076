<template>
  <view class="container">
    <!-- 大转盘 -->
    <view class="dial">
      <!-- <image class="arrow" src="/assets/background/pin_arrow.png"></image> -->
      <image
        class="bg"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        src="/static/images/pins.png"
      />
      <image class="go" @tap="handleTurn" src="/static/images/pins_go.webp"></image>
    </view>


  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const degree = ref(0) // 转盘旋转角度
const duration = ref(0) // 动画持续时间
const isSpinning = ref(false) // 是否正在旋转

// 奖品配置（按照转盘顺序，从上方开始顺时针）
const prizes = ref([
  { name: '家属餐', probability: 70, angle: 0 },      // 上方正中间
  { name: '婴儿游泳', probability: 10, angle: 72 },    // 顺时针第2个
  { name: '尿不湿', probability: 5, angle: 144 },      // 顺时针第3个
  { name: '续住一天', probability: 5, angle: 216 },    // 顺时针第4个
  { name: '产康次数', probability: 10, angle: 288 }    // 顺时针第5个
])

// 当前中奖结果
const currentPrize = ref(null)

// 根据概率计算中奖结果
const calculatePrize = () => {
  const random = Math.random() * 100 // 0-100的随机数
  let cumulativeProbability = 0

  for (const prize of prizes.value) {
    cumulativeProbability += prize.probability
    if (random <= cumulativeProbability) {
      return prize
    }
  }

  // 默认返回第一个奖品（家属餐）
  return prizes.value[0]
}

// 计算转盘应该停止的角度
const calculateStopAngle = (targetPrize) => {
  // 指针指向上方正中间（0度），需要计算转盘应该转到的角度
  // 转盘是顺时针方向，指针固定，转盘逆时针转动让奖品对准指针
  const targetAngle = targetPrize.angle

  // 每个奖品区域占72度，奖品正中间位置 = 起始角度 + 36度
  const prizeCenter = targetAngle + 36 // 奖品区域的正中间

  // 转盘需要转动的角度，让奖品中心对准指针（0度位置）
  // 如果奖品中心在36度，转盘需要逆时针转36度，即转盘角度增加324度
  const stopAngle = 360 - prizeCenter

  console.log(`=== 角度计算详情 ===`)
  console.log(`目标奖品: ${targetPrize.name}`)
  console.log(`奖品起始角度: ${targetAngle}°`)
  console.log(`奖品中心角度: ${prizeCenter}°`)
  console.log(`转盘需要转动: ${stopAngle}°`)
  console.log(`==================`)

  return stopAngle
}

// 点击转盘按钮
const handleTurn = () => {
  // 如果正在旋转，忽略点击
  if (isSpinning.value) {
    console.log('转盘正在旋转中，忽略点击')
    return
  }

  console.log('开始转盘旋转')

  // 计算中奖结果
  const winPrize = calculatePrize()
  currentPrize.value = winPrize
  console.log('中奖结果:', winPrize)

  // 计算停止角度（指向奖品正中间）
  const stopAngle = calculateStopAngle(winPrize)
  console.log('目标停止角度:', stopAngle)

  // 生成随机转动角度（超过1000度）
  const minRotation = 1000 // 最少转动1000度
  const maxRotation = 3600 // 最多转动3600度（10圈）
  const randomRotation = Math.random() * (maxRotation - minRotation) + minRotation

  console.log(`随机转动角度: ${randomRotation}度`)

  // 开始旋转
  isSpinning.value = true

  // 设置旋转参数
  const spinDuration = 4 // 旋转持续时间（秒）
  const totalRotation = randomRotation + stopAngle

  console.log(`=== 转盘转动详情 ===`)
  console.log(`当前转盘角度: ${degree.value}度`)
  console.log(`随机转动角度: ${randomRotation}度`)
  console.log(`精确停止角度: ${stopAngle}度`)
  console.log(`总转动角度: ${totalRotation}度`)
  console.log(`最终转盘角度: ${degree.value + totalRotation}度`)
  console.log(`==================`)

  // 设置动画时间和角度
  duration.value = spinDuration
  degree.value = degree.value + totalRotation

  // 旋转结束后显示结果
  setTimeout(() => {
    isSpinning.value = false
    console.log('转盘旋转结束')
    duration.value = 0

    // 显示中奖结果弹窗
    showPrizeResult()
  }, spinDuration * 1000)
}



// 显示中奖结果弹窗
const showPrizeResult = () => {
  if (currentPrize.value) {
    uni.showModal({
      title: '恭喜中奖！',
      content: `您获得了：${currentPrize.value.name}`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        console.log('用户确认中奖结果')
      }
    })
  }
}
</script>

<style lang="scss" scoped>
/* pages/shop/dial/index.wxss */

.container {
  width: 100vw;
  height: 100vh;
  padding-top:15vh ;
  background: #f8ae61;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.dial .bg {
  width: 700rpx;
  height: 700rpx;
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 先快后慢，慢慢停下来 */
}

.dial .arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%,-50%);
  width: 64rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  margin: auto;
  width: 120rpx;
  height: 140rpx;
  z-index: 100;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.dial .go:hover {
  transform: scale(1.05);
}

.dial .go:active {
  transform: scale(0.95);
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}



/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}




</style>