<template>
  <view class="prize-container">
    <custom-nav-bar title="我的奖品" showBack></custom-nav-bar>
    <!-- 加载状态 -->
    <view class="loading-section" v-if="loading">
      <view class="loading-text">正在加载...</view>
    </view>

    <!-- 无推荐码状态 -->
    <view class="empty-section" v-else-if="!userInfo.referralCode">
      <view class="empty-icon">🎁</view>
      <view class="empty-title">暂无推荐码</view>
      <view class="empty-desc">您还没有推荐码，无法查看奖品</view>
    </view>

    <!-- 有推荐码但无奖品状态 -->
    <view class="empty-section" v-else-if="userInfo.referralCode && (!prizeList || prizeList.length === 0)">

      <view class="empty-icon">🎯</view>
      <view class="empty-title">暂无奖品</view>
      <view class="empty-desc">您还没有获得任何奖品，快去参与抽奖吧！</view>
      <button class="lottery-btn" @tap="goToLottery">
        前往抽奖
      </button>
    </view>

    <!-- 有奖品状态 -->
    <view class="prize-container-content" v-else>
      <view class="prize-header">
        <text class="prize-title">恭喜您获得奖品</text>
      </view>

      <!-- 单个奖品展示 -->
      <view class="single-prize">
        <view class="prize-image-large">
          <image :src="currentPrize.image || '/static/images/default-prize.png'" mode="aspectFill"></image>
        </view>
        <view class="prize-details">
          <view class="prize-name-large">{{ currentPrize.name }}</view>
          <view class="prize-desc-large">{{ currentPrize.description }}</view>
          <view class="prize-time-large">获得时间：{{ currentPrize.createTime }}</view>

        </view>
      </view>


    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getUserInfo } from "@/config/api/user.js"

// 响应式数据
const loading = ref(true)
const userInfo = ref({})
const prizeList = ref([])

// 当前展示的奖品（取第一个奖品）
const currentPrize = computed(() => {
  return prizeList.value && prizeList.value.length > 0 ? prizeList.value[0] : {}
})

// 页面加载时获取用户信息
onMounted(() => {
  fetchUserInfo()

  userInfo.value = {
    referralCode:250729
  }
  prizeList.value = [{
        id: 1,
        name: '优惠券',
        description: '满100减20优惠券',
        image: '/static/images/coupon.png',
        createTime: '2024-01-15 10:30:00',
        status: 0 // 0-未使用，1-已使用
      }]

})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true
    console.log('开始获取用户信息...')

    const response = await getUserInfo()
    console.log('用户信息获取结果:', response)

    if (response && response.data) {
      userInfo.value = response.data
      console.log('用户推荐码:', userInfo.value.referralCode)

      // 如果有推荐码，获取奖品列表
      if (userInfo.value.referralCode) {
        await fetchPrizeList()
      }
      
    }

  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取奖品列表（模拟数据，实际项目中需要调用真实API）
const fetchPrizeList = async () => {
  try {
    console.log('开始获取奖品列表...')

    // 这里应该调用真实的获取奖品列表API
    // const response = await getPrizeList(userInfo.value.referralCode)

    // 模拟奖品数据
    const mockPrizes = [
      {
        id: 1,
        name: '优惠券',
        description: '满100减20优惠券',
        image: '/static/images/coupon.png',
        createTime: '2024-01-15 10:30:00',
        status: 0 // 0-未使用，1-已使用
      },
      {
        id: 2,
        name: '积分奖励',
        description: '100积分',
        image: '/static/images/points.png',
        createTime: '2024-01-14 15:20:00',
        status: 1
      }
    ]

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 根据用户推荐码决定是否有奖品（这里简单模拟）
    if (userInfo.value.referralCode && userInfo.value.referralCode.length > 0) {
      // 50% 概率有奖品（实际项目中根据真实数据）
      prizeList.value = Math.random() > 0.5 ? mockPrizes : []
    } else {
      prizeList.value = []
    }

    console.log('奖品列表:', prizeList.value)

  } catch (error) {
    console.error('获取奖品列表失败:', error)
    prizeList.value = []
  }
}

// 前往抽奖
const goToLottery = () => {
  console.log('前往抽奖页面')
  uni.navigateTo({
    url: '/pages_business/pages/turntable/turntable'
  })
}
</script>

<style lang="scss" scoped>
.prize-container {
  min-height: 100vh;
  background: #FAFAF5;
  padding: 40rpx 30rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .loading-text {
    font-size: 32rpx;
    color: #666;
  }
}

/* 空状态样式 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 40rpx;
  }

  .empty-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 60rpx;
  }
}

/* 抽奖按钮 */
.lottery-btn {
  width: 100%;
  height: 96rpx;
  background: #8b5a2b;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 6rpx 15rpx rgba(52, 152, 219, 0.3);
  }


}

/* 奖品容器 */
.prize-container-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.prize-header {
  text-align: center;
  margin-bottom: 60rpx;

  .prize-title {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
  }
}

/* 单个奖品展示 */
.single-prize {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 60rpx;
}

.prize-image-large {
  width: 300rpx;
  height: 300rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
  background: #f8f9fa;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);

  image {
    width: 100%;
    height: 100%;
  }
}

.prize-details {
  width: 100%;

  .prize-name-large {
    font-size: 42rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .prize-desc-large {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 30rpx;
    line-height: 1.5;
  }

  .prize-time-large {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 30rpx;
  }
}




</style>