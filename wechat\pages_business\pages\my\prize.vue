<template>
  <view class="prize-container">
    <custom-nav-bar title="我的奖品" showBack></custom-nav-bar>
    <!-- 加载状态 -->
    <view class="loading-section" v-if="loading">
      <view class="loading-text">正在加载...</view>
    </view>

    <!-- 无推荐码状态 -->
    <view class="empty-section" v-else-if="!userInfo.referralCode">
      <view class="empty-icon">🎁</view>
      <view class="empty-title">暂无推荐码</view>
      <view class="empty-desc">您还没有推荐码，无法查看奖品</view>
    </view>

    <!-- 有推荐码但无奖品状态 -->
    <view class="empty-section" v-else-if="userInfo.referralCode && (!prizeList || prizeList.length === 0)">
      <view class="empty-icon">🎯</view>
      <view class="empty-title">暂无奖品</view>
      <view class="empty-desc">您还没有获得任何奖品，快去参与抽奖吧！</view>
      <button class="lottery-btn" @tap="goToLottery">
        前往抽奖
      </button>
    </view>

    <!-- 有奖品状态 -->
    <view class="prize-list" v-else>
      <view class="prize-header">
        <text class="prize-title">我的奖品</text>
        <text class="prize-count">共{{ prizeList.length }}个奖品</text>
      </view>

      <view class="prize-item" v-for="(item, index) in prizeList" :key="index">
        <view class="prize-image">
          <image :src="item.image || '/static/images/default-prize.png'" mode="aspectFill"></image>
        </view>
        <view class="prize-info">
          <view class="prize-name">{{ item.name }}</view>
          <view class="prize-desc">{{ item.description }}</view>
          <view class="prize-time">获得时间：{{ item.createTime }}</view>
        </view>
        <view class="prize-status">
          <text :class="['status-text', item.status === 1 ? 'status-used' : 'status-unused']">
            {{ item.status === 1 ? '已使用' : '未使用' }}
          </text>
        </view>
      </view>

      <!-- 前往抽奖按钮 -->
      <view class="lottery-section">
        <button class="lottery-btn secondary" @tap="goToLottery">
          继续抽奖
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUserInfo } from "@/config/api/user.js"

// 响应式数据
const loading = ref(true)
const userInfo = ref({})
const prizeList = ref([])

// 页面加载时获取用户信息
onMounted(() => {
  fetchUserInfo()
})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true
    console.log('开始获取用户信息...')

    const response = await getUserInfo()
    console.log('用户信息获取结果:', response)

    if (response && response.data) {
      userInfo.value = response.data
      console.log('用户推荐码:', userInfo.value.referralCode)

      // 如果有推荐码，获取奖品列表
      if (userInfo.value.referralCode) {
        await fetchPrizeList()
      }
      userInfo.value = {
        referralCode:250729,
        
      }
    }

  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取奖品列表（模拟数据，实际项目中需要调用真实API）
const fetchPrizeList = async () => {
  try {
    console.log('开始获取奖品列表...')

    // 这里应该调用真实的获取奖品列表API
    // const response = await getPrizeList(userInfo.value.referralCode)

    // 模拟奖品数据
    const mockPrizes = [
      {
        id: 1,
        name: '优惠券',
        description: '满100减20优惠券',
        image: '/static/images/coupon.png',
        createTime: '2024-01-15 10:30:00',
        status: 0 // 0-未使用，1-已使用
      },
      {
        id: 2,
        name: '积分奖励',
        description: '100积分',
        image: '/static/images/points.png',
        createTime: '2024-01-14 15:20:00',
        status: 1
      }
    ]

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 根据用户推荐码决定是否有奖品（这里简单模拟）
    if (userInfo.value.referralCode && userInfo.value.referralCode.length > 0) {
      // 50% 概率有奖品（实际项目中根据真实数据）
      prizeList.value = Math.random() > 0.5 ? mockPrizes : []
    } else {
      prizeList.value = []
    }

    console.log('奖品列表:', prizeList.value)

  } catch (error) {
    console.error('获取奖品列表失败:', error)
    prizeList.value = []
  }
}

// 前往抽奖
const goToLottery = () => {
  console.log('前往抽奖页面')
  uni.navigateTo({
    url: '/pages_business/pages/turntable/turntable'
  })
}
</script>

<style lang="scss" scoped>
.prize-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 30rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .loading-text {
    font-size: 32rpx;
    color: #666;
  }
}

/* 空状态样式 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 40rpx;
  }

  .empty-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 60rpx;
  }
}

/* 抽奖按钮 */
.lottery-btn {
  padding: 24rpx 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 6rpx 15rpx rgba(102, 126, 234, 0.3);
  }

  &.secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
    box-shadow: 0 8rpx 20rpx rgba(168, 237, 234, 0.3);

    &:active {
      box-shadow: 0 6rpx 15rpx rgba(168, 237, 234, 0.3);
    }
  }
}

/* 奖品列表 */
.prize-list {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.prize-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;

  .prize-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .prize-count {
    font-size: 26rpx;
    color: #999;
  }
}

.prize-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.prize-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 30rpx;
  background: #f8f9fa;

  image {
    width: 100%;
    height: 100%;
  }
}

.prize-info {
  flex: 1;

  .prize-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 10rpx;
  }

  .prize-desc {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
  }

  .prize-time {
    font-size: 24rpx;
    color: #999;
  }
}

.prize-status {
  .status-text {
    padding: 8rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;

    &.status-unused {
      background: #e8f5e8;
      color: #27ae60;
    }

    &.status-used {
      background: #f5f5f5;
      color: #999;
    }
  }
}

.lottery-section {
  margin-top: 60rpx;
  text-align: center;
  padding-top: 40rpx;
  border-top: 2rpx solid #f0f0f0;
}
</style>