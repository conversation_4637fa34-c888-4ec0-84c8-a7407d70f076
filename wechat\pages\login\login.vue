<template>
	<view class="login-container">
		<view class="nav-back" :style="{ top: `${navBarHeight}rpx` }" @click="handleBack">
			<up-icon name="arrow-left" color="#8b5a2b" size="20"></up-icon>
		</view>
		<view class="login-content">
			<view class="login-header">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<!-- <text class="title">用户登录</text> -->
			</view>

			<view class="login-form">
				<!-- <view class="welcome-text">
					<text>欢迎使用东方爱堡月子会所</text>
					<text class="sub-text">请使用微信账号快速登录</text>
				</view> -->

				<!-- 微信一键登录按钮 -->
				<button class="wechat-login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
					<!-- <text class="iconfont wechat-icon">&#xe607;</text> -->
					手机号快捷登录
				</button>
				

				<!-- 开发环境下的快速登录按钮 -->
				<!-- <view v-if="isDev" class="dev-login-section">
					<text class="dev-tip">开发环境：请输入手机号作为用户ID</text>
					<view class="form-item">
						<input class="input" v-model="devPhone" placeholder="请输入手机号" type="number" maxlength="11" />
					</view>
					<button class="dev-login-btn" @click="handleDevLogin">开发环境快速登录</button>
				</view> -->
			</view>
	
		</view>
		</view>
</template>

<script>
	import { API, request } from '../../config/api.js';
	
	import { login } from "@/config/api/login.js";
	import { getUserInfo } from "@/config/api/user.js";

	export default {
		data() {
			return {
				devPhone: '', // 开发环境下使用的手机号
				isDev: process.env.NODE_ENV === 'development',
				navBarHeight:0
			};
		},
		mounted(){
			const menuButton = uni.getMenuButtonBoundingClientRect();
			this.navBarHeight = (((menuButton.height - 20) / 2) + menuButton.top) * 2;
			console.log(this.navBarHeight)
		},
		methods: {
			/**
			 * 获取微信手机号
			 * 微信小程序一键登录回调
			 */
			getPhoneNumber(e) {
				// 用户拒绝授权
				if (e.detail.errMsg !== 'getPhoneNumber:ok') {
					return uni.showToast({
						title: '获取手机号失败，请重试',
						icon: 'none'
					});
				}

				// 显示加载中
				uni.showLoading({
					title: '登录中...',
					mask: true
				});

				// 获取微信登录凭证
				uni.login({
					provider: 'weixin',
					success: res => {
						if (res.code) {
							// 构造登录请求
							const params = {
								code: res.code,
								phoneCode : e.detail.code
							};
							// 调用登录接口
							this.loginWithWechat(params);
							
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '微信登录失败，请重试',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({
							title: '微信登录失败，请重试',
							icon: 'none'
						});
					}
				});
			},
			/**
			 * 使用微信信息登录
			 */
			loginWithWechat(params) {
				login(params).then(async res =>{
					uni.setStorageSync('token', res.token);
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
					await this.getUserInfo()
					setTimeout(() => {
						uni.reLaunch({
							url:'/pages/my/my'
						})
					},500)
					
				}).catch(err => {
					uni.showToast({
						title: err.message || '登录失败，请重试',
						icon: 'none',
						duration: 3000
					});
				}).finally(() => {
					uni.hideLoading();
				})
					
			},
			
			getUserInfo(){
				getUserInfo({}).then(res => {
					if(res.openId) delete res.openId
					uni.setStorageSync('userInfo', JSON.stringify(res));
				})
			},

			/**
			 * 开发环境快速登录
			 * 使用手机号作为openId进行登录
			 */
			handleDevLogin() {
				// 验证手机号
				if (!this.devPhone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}

				// 显示加载中
				uni.showLoading({
					title: '登录中...',
					mask: true
				});

				// 使用手机号作为openId
				const openId = this.devPhone;

				// 构造登录请求
				const loginData = {
					code: openId, // 使用手机号作为code（实际项目中应该使用微信登录获取的code）
					phoneNumber: this.devPhone, // 添加手机号
					wxNickname: '测试用户',
					wxAvatarUrl: '/static/logo.png',
					wxGender: 1,
					wxCountry: '中国',
					wxProvince: '广东省',
					wxCity: '深圳市'
				};

				// 调用登录接口
				request({
					url: API.appUser.login,
					method: 'POST',
					data: loginData
				}).then(res => {
					uni.hideLoading();

					// 保存用户信息和token
					uni.setStorageSync('userInfo', JSON.stringify(res.userInfo));
					uni.setStorageSync('token', res.token);

					uni.showToast({
						title: '开发环境登录成功',
						icon: 'success'
					});

					// 跳转到首页
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						});
					}, 1500);
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.message || '登录失败',
						icon: 'none',
						duration: 3000
					});
				});
			},
			handleBack(){
				uni.switchTab({
					url: '/pages/index/index'
				});
			}
		}
	};
</script>

<style>
	.login-container {
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		min-height: 100vh;
		background-color: #FAFAF5;
	}
	.login-content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100%;
	}

	.login-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 60rpx;
	}

	.logo {
		width: 250rpx;
		height: 250rpx;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		word-break:break-all;
	}

	.login-form {
		width: 100%;
		/* background-color: #fff; */
		padding: 40rpx;
		border-radius: 12rpx;
		/* box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); */
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.welcome-text {
		text-align: center;
		margin-bottom: 60rpx;
	}

	.welcome-text text {
		display: block;
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.welcome-text .sub-text {
		font-size: 26rpx;
		color: #999;
		font-weight: normal;
	}

	.wechat-login-btn {
		background-color: #CEB684;
		color: #fff;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		width: 80%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.wechat-icon {
		margin-right: 10rpx;
		font-size: 36rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
		width: 100%;
	}

	.input {
		height: 80rpx;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.dev-login-section {
		margin-top: 60rpx;
		border-top: 1px dashed #ddd;
		padding-top: 30rpx;
		width: 100%;
	}

	.dev-tip {
		display: block;
		font-size: 24rpx;
		color: #ff9800;
		margin-bottom: 15rpx;
		text-align: center;
	}

	.dev-login-btn {
		background-color: #1890ff;
		color: #fff;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		width: 100%;
	}
	.nav-back {
		position: absolute;
		left: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 80rpx;
		height: 80rpx;
	}
</style>
