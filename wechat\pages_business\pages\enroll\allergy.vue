<template>
	<view class="dietary-container">
		<!-- 自定义导航栏 -->
		<custom-nav-bar title="用餐忌口" showBack></custom-nav-bar>

		<view class="form-container">
			<view class="form-item">
				<text class="item-label required">姓名</text>
				<input type="text" v-model="dietaryForm.userName" :disabled="dietaryForm.userNameDisabled || false" placeholder="请输入宝妈姓名" />
				<text class="error-tip" v-if="errors.userName">{{ errors.userName }}</text>
			</view>

			<view class="form-item">
				<text class="item-label required">房间号</text>
				<input type="text" v-model="dietaryForm.roomNumber" :disabled="dietaryForm.roomNumberDisabled || false" placeholder="请输入房间号" />
				<text class="error-tip" v-if="errors.roomNumber">{{ errors.roomNumber }}</text>
			</view>

			<view class="form-item">
				<text class="item-label">用餐忌口</text>

				<!-- 富文本编辑器工具栏 -->
				<view class="editor-toolbar">
					<view class="color-buttons">
						<view
							class="color-btn red"
							:class="{ 'active': currentColor === '#ff0000' }"
							@click="setTextColor('#ff0000')"
						>
							<text class="color-text">红</text>
						</view>
						<view
							class="color-btn green"
							:class="{ 'active': currentColor === '#00aa00' }"
							@click="setTextColor('#00aa00')"
						>
							<text class="color-text">绿</text>
						</view>
						<view
							class="color-btn blue"
							:class="{ 'active': currentColor === '#0066cc' }"
							@click="setTextColor('#0066cc')"
						>
							<text class="color-text">蓝</text>
						</view>
						<view
							class="color-btn default"
							:class="{ 'active': currentColor === '#333333' }"
							@click="setTextColor('#333333')"
						>
							<text class="color-text">默认</text>
						</view>
					</view>
				</view>

				<!-- 富文本编辑器 -->
				<editor
					id="allergyEditor"
					class="rich-editor"
					:placeholder="editorPlaceholder"
					@ready="onEditorReady"
					@input="onEditorInput"
					@focus="onEditorFocus"
					@blur="onEditorBlur"
					:show-img-size="false"
					:show-img-toolbar="false"
					:show-img-resize="false"
				></editor>

				<view class="char-count">
					<text class="count-text" :class="{ 'count-warning': showWarning }">
						{{ currentCharLength }}/50
					</text>
				</view>
				<text class="error-tip" v-if="errors.allergyDetail">{{ errors.allergyDetail }}</text>
			</view>

			<button class="submit-btn" @click="submitDietaryForm">提交忌口信息</button>
		</view>

		<!-- 历史记录展示 -->
		<view class="history-container" v-if="historyData.length > 0">
			<view class="history-header" @click="toggleHistoryExpand">
				<view class="header-left">
					<text class="history-title">历史记录</text>
					<text class="history-count">({{ historyData.length }}条)</text>
				</view>
				<view class="header-right">
					<text class="expand-text">{{ isHistoryExpanded ? '收起' : '展开' }}</text>
					<view class="expand-icon" :class="{ 'expanded': isHistoryExpanded }">
						<text class="icon-arrow">▼</text>
					</view>
				</view>
			</view>

			<view class="history-list" v-show="isHistoryExpanded">
				<view
					class="history-item"
					v-for="(item, index) in historyData"
					:key="index"
				>
					<view class="history-item-header">
						<view class="history-content">
							<text class="content-label">创建时间:</text>
							<text class="content-text">{{ formatTime(item.createTime || item.updateTime) }}</text>
						</view>
					</view>

					<view class="history-content" v-if="item.allergyDetail">
						<text class="content-label">忌口信息:</text>
						<rich-text
							class="content-rich-text"
							:nodes="formatRichText(item.allergyDetail)"
						></rich-text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="historyData.length === 0">
			<text class="empty-text">暂无历史记录</text>
			<text class="empty-tip">提交忌口信息后将显示在这里</text>
		</view>
	</view>
</template>
<script setup>
	import { reactive,ref,toRaw,computed } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import { listMemberCheckin } from "@/config/api/member_check_in.js";
	import { getAllergyInfo,saveAllergyInfo,getAllergyList } from "@/config/api/allergy.js";
	import CustomNavBar from '@/components/custom-nav-bar/custom-nav-bar.vue';
	
	const dietaryForm = reactive({
	  userName: '',
	  roomNumber: '',
	  allergyDetail: ''
	});
	const historyData = reactive([]);
	const errors = reactive({});
	const isEdit = ref(false);
	const isCheckIn = ref(false);

	// 历史记录展开/收起状态
	const isHistoryExpanded = ref(false);

	// 富文本编辑器相关状态
	const editorContext = ref(null);
	const currentColor = ref('#333333');
	const editorPlaceholder = '请输入您的用餐忌口信息，限制50字内';
	// 记录上一次的内容，用于检测是否从空内容开始输入
	const lastContent = ref('');

	// 计算当前字符长度
	const currentCharLength = computed(() => {
		return getCharLength(dietaryForm.allergyDetail);
	});

	// 是否显示警告颜色
	const showWarning = computed(() => {
		return currentCharLength.value > 45;
	});

	// 安全计算字符长度的函数（去除HTML标签后计算纯文本长度）
	const getCharLength = (str) => {
		if (!str || typeof str !== 'string') {
			return 0;
		}
		// 去除HTML标签，只计算纯文本长度
		const plainText = str.replace(/<[^>]*>/g, '');
		return plainText.length;
	}

	// 格式化富文本内容用于显示
	const formatRichText = (htmlContent) => {
		if (!htmlContent) return '';
		// 如果内容不包含HTML标签，则包装为普通文本
		if (!/<[^>]*>/.test(htmlContent)) {
			return htmlContent;
		}
		return htmlContent;
	}

	// 富文本编辑器准备就绪
	const onEditorReady = () => {
		uni.createSelectorQuery().select('#allergyEditor').context((res) => {
			editorContext.value = res.context;
			// 如果有初始内容，设置到编辑器中
			if (dietaryForm.allergyDetail) {
				editorContext.value.setContents({
					html: dietaryForm.allergyDetail
				});
				// 初始化记录的内容
				lastContent.value = dietaryForm.allergyDetail;
			} else {
				// 如果没有初始内容，确保记录为空
				lastContent.value = '';
			}
		}).exec();
	};

	// 富文本编辑器内容变化
	const onEditorInput = (e) => {
		const htmlContent = e.detail.html || '';
		const plainTextLength = getCharLength(htmlContent);

		// 检查纯文本长度是否超过50个字符
		if (plainTextLength > 50) {
			// 恢复到上一次的内容，阻止超出限制的输入
			if (editorContext.value && lastContent.value) {
				editorContext.value.setContents({
					html: lastContent.value
				});
			}
			uni.showToast({
				title: '最多只能输入50个字符',
				icon: 'none',
				duration: 1000
			});
			return;
		}

		// 检测是否从空内容开始输入新文字
		const wasEmpty = !lastContent.value || getCharLength(lastContent.value) === 0;
		const hasContent = plainTextLength > 0;

		// 如果之前是空的，现在有内容了，且当前选中的颜色不是默认颜色，则应用颜色
		if (wasEmpty && hasContent && currentColor.value !== '#333333') {
			// 延迟执行，确保编辑器内容已更新
			setTimeout(() => {
				if (editorContext.value) {
					// 应用当前选中的颜色到新输入的文字
					editorContext.value.format('color', currentColor.value);
				}
			}, 50);
		}

		// 更新记录的内容
		lastContent.value = htmlContent;
		dietaryForm.allergyDetail = htmlContent;
	};

	// 富文本编辑器获得焦点
	const onEditorFocus = () => {
		// 可以在这里添加获得焦点时的逻辑
	};

	// 富文本编辑器失去焦点
	const onEditorBlur = () => {
		// 可以在这里添加失去焦点时的逻辑
	};

	// 设置文字颜色
	const setTextColor = (color) => {
		currentColor.value = color;
		if (editorContext.value) {
			editorContext.value.format('color', color);
		}
	};

	const handleGetAllergyInfo = async () => {
		try {
		    const response = await getAllergyList({}); // 你的 API 函数
			if(response.length > 0){
				Object.assign(dietaryForm, response[0]);
				// 确保 allergyDetail 是字符串类型
				if (dietaryForm.allergyDetail && typeof dietaryForm.allergyDetail !== 'string') {
					dietaryForm.allergyDetail = String(dietaryForm.allergyDetail);
				}
				if (!dietaryForm.allergyDetail) {
					dietaryForm.allergyDetail = '';
				}
				Object.assign(historyData, response);
				// 有历史数据时默认展开
				isHistoryExpanded.value = true;
			}

		  } catch (error) {
		    console.error('获取数据失败:', error);
		  }
	}
	
	// 表单验证方法
	const validateForm = () => {
	  errors.userName = '';
	  errors.roomNumber = '';
	  errors.allergyDetail = '';
	  let isValid = true;

	  if (!dietaryForm.userName) {
	    errors.userName = '请输入宝妈姓名';
	    isValid = false;
	  }

	  if (!dietaryForm.roomNumber) {
	    errors.roomNumber = '请输入房间号';
	    isValid = false;
	  }

	  if (dietaryForm.allergyDetail && getCharLength(dietaryForm.allergyDetail) > 50) {
	    errors.allergyDetail = '用餐忌口信息不能超过50个字符';
	    isValid = false;
	  }

	  return isValid;
	};
	
	// 表单提交
	const submitDietaryForm = async () => {
		if(!isCheckIn.value){
			return uni.showToast({
				title: '您还未入住，请先办理入住',
				icon: 'none'
			});
		}
		
		
	  if (!validateForm()) {
	    return uni.showToast({
	      title: '请完善必填信息',
	      icon: 'none'
	    });
	  }
	  
	  try {
		  const params = toRaw(dietaryForm)
		  delete params.id
		  delete params.openId
		  delete params.createTime
		  const response = await saveAllergyInfo(params);
		  uni.showToast({
		    title: '忌口信息提交成功',
		    icon: 'success'
		  });
		  setTimeout(() => {
		    uni.navigateBack();
		  }, 1000);
	  } catch (error) {
		  uni.showToast({
			title: '提交失败，请稍后重试',
			icon: 'none'
		  });
	  }
	};

	// 格式化时间
	const formatTime = (timeStr) => {
		if (!timeStr) return '未知时间';

		try {
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');

			return `${year}-${month}-${day} ${hours}:${minutes}`;
		} catch (error) {
			return '时间格式错误';
		}
	};

	// 选择历史记录项
	const selectHistoryItem = (item) => {
		uni.showModal({
			title: '提示',
			content: '是否要使用此历史记录的信息？',
			success: (res) => {
				if (res.confirm) {
					// 复制历史记录到当前表单
					dietaryForm.userName = item.userName || '';
					dietaryForm.roomNumber = item.roomNumber || '';
					dietaryForm.allergyDetail = String(item.allergyDetail || '');

					// 更新富文本编辑器内容
					if (editorContext.value) {
						editorContext.value.setContents({
							html: dietaryForm.allergyDetail
						});
					}

					// 更新记录的内容
					lastContent.value = dietaryForm.allergyDetail;

					uni.showToast({
						title: '已应用历史记录',
						icon: 'success'
					});
				}
			}
		});
	};

	// 编辑历史记录项
	const editHistoryItem = (item) => {
		// 复制历史记录到当前表单进行编辑
		dietaryForm.userName = item.userName || '';
		dietaryForm.roomNumber = item.roomNumber || '';
		dietaryForm.allergyDetail = String(item.allergyDetail || '');

		// 更新富文本编辑器内容
		if (editorContext.value) {
			editorContext.value.setContents({
				html: dietaryForm.allergyDetail
			});
		}

		// 更新记录的内容
		lastContent.value = dietaryForm.allergyDetail;

		// 设置编辑模式
		isEdit.value = true;

		uni.showToast({
			title: '已加载到编辑区域',
			icon: 'success'
		});

		// 滚动到表单顶部
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 300
		});
	};

	// 切换历史记录展开/收起状态
	const toggleHistoryExpand = () => {
		isHistoryExpanded.value = !isHistoryExpanded.value;
	};

	// 删除历史记录项
	const deleteHistoryItem = (item, index) => {
		uni.showModal({
			title: '确认删除',
			content: '确定要删除这条历史记录吗？',
			success: async (res) => {
				if (res.confirm) {
					try {
						// 这里可以调用删除API，如果有的话
						// await deleteAllergyInfo(item.id);

						// 从本地数组中删除
						historyData.splice(index, 1);

						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					} catch (error) {
						console.error('删除失败:', error);
						uni.showToast({
							title: '删除失败',
							icon: 'none'
						});
					}
				}
			}
		});
	};

	onLoad(async (options) => {
		await handleGetAllergyInfo()

		// 确保 allergyDetail 字段的数据类型正确
		if (typeof dietaryForm.allergyDetail !== 'string') {
			dietaryForm.allergyDetail = String(dietaryForm.allergyDetail || '');
		}

		const response = await listMemberCheckin({});
		let memberInfo = {};
		if(typeof response === 'object'){
			uni.setStorageSync('memberInfo', JSON.stringify(response));
			memberInfo = response
		}
		if(Object.keys(memberInfo).length !== 0){
			isCheckIn.value = memberInfo.status !== 1

			if(memberInfo.memberName){
				dietaryForm.userName = memberInfo.memberName
				dietaryForm.userNameDisabled = true
			}

			if(memberInfo.roomNumber){
				dietaryForm.roomNumber = memberInfo.roomNumber
				dietaryForm.roomNumberDisabled = true
			}
		}
	});
</script>

<style lang="scss" scoped>
	.dietary-container {
		padding: 0 20rpx 20rpx;
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.header {
		padding: 20rpx 0;
		margin-bottom: 30rpx;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #8b5a2b; /* 与首页标题颜色一致 */
	}

	.form-container {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		margin-top: 20rpx;
	}
	
	.form-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
	}
	
	.form-item:first-child {
		padding-top: 0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.item-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.required:after {
		content: '*';
		color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		margin-left: 5rpx;
	}

	.error-tip {
		font-size: 24rpx;
		color: #FF4D4F;
		margin-top: 8rpx;
	}

	.char-count {
		display: flex;
		justify-content: flex-end;
		margin-top: 8rpx;
	}

	.count-text {
		font-size: 24rpx;
		color: #999;
	}

	.count-warning {
		color: #ff6b35 !important;
	}

	.form-item input,
	.form-item textarea{
		width: 100%;
		font-size: 28rpx;
		padding: 15rpx 20rpx;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
	}

	/* 富文本编辑器工具栏样式 */
	.editor-toolbar {
		margin-bottom: 15rpx;
		padding: 15rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		border: 1rpx solid #eee;
	}

	.color-buttons {
		display: flex;
		gap: 15rpx;
		align-items: center;
	}

	.color-btn {
		padding: 8rpx 16rpx;
		border-radius: 6rpx;
		border: 1rpx solid #ddd;
		background-color: #fff;
		cursor: pointer;
		transition: all 0.3s ease;
		min-width: 60rpx;
		text-align: center;
	}

	.color-btn.red {
		border-color: #ff0000;
	}

	.color-btn.red.active {
		background-color: #ff0000;
		color: #fff;
	}

	.color-btn.green {
		border-color: #00aa00;
	}

	.color-btn.green.active {
		background-color: #00aa00;
		color: #fff;
	}

	.color-btn.blue {
		border-color: #0066cc;
	}

	.color-btn.blue.active {
		background-color: #0066cc;
		color: #fff;
	}

	.color-btn.default {
		border-color: #333333;
	}

	.color-btn.default.active {
		background-color: #333333;
		color: #fff;
	}

	.color-text {
		font-size: 24rpx;
		font-weight: bold;
	}

	/* 富文本编辑器样式 */
	.rich-editor {
		width: 100%;
		min-height: 200rpx;
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 15rpx;
		background-color: #fff;
		font-size: 28rpx;
	}

	/* 历史记录中的富文本显示样式 */
	.content-rich-text {
		font-size: 28rpx;
		line-height: 1.6;
		word-break: break-all;
		flex: 1;
	}

	/* input, textarea {
		border: 1rpx solid #eee;
		padding: 15rpx 20rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		width: 100%;
		box-sizing: border-box;
	}

	textarea {
		height: 300rpx;
	} */

	.picker-value {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #333;
		border: 1rpx solid #eee;
		padding: 0 20rpx;
		border-radius: 8rpx;
		background-color: #fff;
	}

	.checkbox-group {
		display: flex;
		flex-wrap: wrap;
		margin: 10rpx 0;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
		margin-bottom: 20rpx;
	}

	.checkbox-item text {
		font-size: 26rpx;
		margin-left: 8rpx;
	}

	.other-input {
		margin-top: 20rpx;
		display: flex;
		align-items: center;
	}

	.other-label {
		font-size: 26rpx;
		color: #666;
		white-space: nowrap;
		margin-right: 10rpx;
	}

	.submit-btn {
		background-color: #8b5a2b !important; /* 深棕色，与首页标题颜色一致 */
		color: #fff !important;
		border-radius: 50rpx;
		font-size: 32rpx;
		margin-top: 40rpx;
		font-weight: bold;
		border: none !important;
		outline: none !important;
	}

	/* 确保按钮在各种状态下都保持正确的颜色 */
	.submit-btn::after {
		border: none !important;
	}

	.submit-btn:hover {
		background-color: #7A4F26 !important;
		color: #fff !important;
	}

	.submit-btn:active {
		background-color: #6B4421 !important;
		color: #fff !important;
	}

	/* 历史记录样式 */
	.history-container {
		background-color: #fff;
		border-radius: 12rpx;
		margin-top: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		overflow: hidden;
	}

	.history-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		background-color: #f8f5f2;
		border-bottom: 1rpx solid #eee;
		cursor: pointer;
		transition: background-color 0.3s;
	}

	.history-header:active {
		background-color: #f0ede8;
	}

	.header-left {
		display: flex;
		align-items: center;
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 10rpx;
	}

	.history-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
	}

	.history-count {
		font-size: 24rpx;
		color: #999;
		margin-left: 10rpx;
	}

	.expand-text {
		font-size: 26rpx;
		color: #8b5a2b;
	}

	.expand-icon {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40rpx;
		height: 40rpx;
		transition: transform 0.3s ease;
	}

	.expand-icon.expanded {
		transform: rotate(180deg);
	}

	.icon-arrow {
		font-size: 24rpx;
		color: #8b5a2b;
		line-height: 1;
	}

	.history-list {
		padding: 0;
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.history-item {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		transition: background-color 0.3s;
	}

	.history-item:last-child {
		border-bottom: none;
	}

	.history-item:active {
		background-color: #f8f5f2;
	}

	.history-item-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 20rpx;
	}

	.history-info {
		display: flex;
		align-items: center;
		flex: 1;
	}
	
	.history-title {
		font-size: 26rpx;
		color: #8b5a2b;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.history-name {
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		word-break: break-all;
	}

	.history-room {
		font-size: 26rpx;
		color: #666;
	}

	.history-time {
		font-size: 24rpx;
		color: #999;
		white-space: nowrap;
		margin-left: 20rpx;
	}
	
	.history-content{
		display: flex;
		align-items: flex-start;
	}

	.content-label {
		font-size: 27rpx;
		color: #666;
		font-weight: bold;
		display: block;
		margin-right: 10rpx;
		min-width: 120rpx;
		line-height: 1.7;
	}

	.content-text {
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		word-break: break-all;
	}

	.history-actions {
		display: flex;
		justify-content: flex-end;
		gap: 20rpx;
	}

	.action-btn {
		padding: 10rpx 20rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		border: none;
		outline: none;
	}

	.edit-btn {
		background-color: #8b5a2b;
		color: #fff;
	}

	.edit-btn::after {
		border: none;
	}

	.delete-btn {
		background-color: #ff4757;
		color: #fff;
	}

	.delete-btn::after {
		border: none;
	}

	/* 空状态样式 */
	.empty-state {
		text-align: center;
		padding: 80rpx 30rpx;
		background-color: #fff;
		border-radius: 12rpx;
		margin-top: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.empty-text {
		font-size: 32rpx;
		color: #999;
		display: block;
		margin-bottom: 20rpx;
	}

	.empty-tip {
		font-size: 26rpx;
		color: #ccc;
	}
</style>
