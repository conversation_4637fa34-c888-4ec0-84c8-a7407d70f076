<template>
	<view class="intro-container">
		<custom-nav-bar title="优质环境"></custom-nav-bar>
		<view class="intro-content">
			<!-- 主要介绍图片 -->
			<!-- <image
				class="intro-image"
				:src="mainImageUrl"
				mode="aspectFill"
				@click="previewImage(mainImageUrl, mainImageLargeUrl)"
			></image> -->
			<!-- <view class="intro-text">
				<view class="intro-title">舒适优雅的环境</view>
				<view class="intro-desc">
					我们的月子中心拥有优雅舒适的环境，每一处细节都经过精心设计，为产妇和新生儿提供温馨、安全的休养空间。现代化的设施配备，让您在这里享受到家一般的温暖。
				</view>
			</view> -->

			<!-- <view class="intro-section">
				<view class="section-title">我们的优势</view>
				<view class="advantage-list">
					<view class="advantage-item">
						<view class="advantage-icon">🏆</view>
						<view class="advantage-text">专业团队，经验丰富</view>
					</view>
					<view class="advantage-item">
						<view class="advantage-icon">🏠</view>
						<view class="advantage-text">环境舒适，设施完善</view>
					</view>
					<view class="advantage-item">
						<view class="advantage-icon">🍲</view>
						<view class="advantage-text">营养月子餐，科学调配</view>
					</view>
					<view class="advantage-item">
						<view class="advantage-icon">👶</view>
						<view class="advantage-text">婴儿护理，细致入微</view>
					</view>
				</view>
			</view> -->

			<view class="intro-section">
				<view class="env-grid">
					<view
						class="env-item"
						v-for="(item, index) in envList"
						:key="index"
						@click="previewImage(item.mediumImage, item.largeImage)"
					>
						<image
							class="env-image"
							:src="item.mediumImage"
							mode="aspectFill"
							:lazy-load="true"
						></image>
						<view class="env-name">{{item.name}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue';
	import imageApi from '@/config/api/image.js';
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 优质环境页面
	 * 展示月子中心的环境图片，从后端读取
	 */

	// 后端图片ID列表
	const imageIds = [
		'1927004827662012417',
		'1927005045602242561',
		'1927004903948013570',
		'1927004952916512769',
		'1927004996927344642',
		'1927005098152677377',
		'1927005166779879426',
		'1927005313249169410',
		'1927005363006197761',
		'1927005407792975874',
		'1927005477280010241',
		'1927005520057716738'
	];

	// 图片名称列表
	const imageNames = [
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' ',
		' '
	];

	// 主要展示图片（使用第一张图片）
	const mainImageUrl = computed(() => imageApi.getMediumImageUrl(imageIds[0]));
	const mainImageLargeUrl = computed(() => imageApi.getLargeImageUrl(imageIds[0]));

	// 环境图片列表 - 使用封装的API生成
	const envList = ref(imageApi.generateImageList(imageIds, imageNames));

	// 图片预览功能 - 使用封装的API
	const previewImage = (currentImage, largeImage) => {
		imageApi.previewImage(currentImage, largeImage);
	};

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 优质环境展示',
			path: '/pages_business/pages/intro/intro',
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所优质环境，为您提供舒适温馨的月子体验',
		});
	}

	onMounted(() => {
		console.log('优质环境页面加载完成');
		console.log('环境图片列表:', envList.value);
	});
</script>

<style>
	.intro-container {
		background-color: #f8f5f2;
		min-height: 100vh;
		padding-bottom: 40rpx;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	.intro-content {
		padding: 20rpx;
	}

	.intro-image {
		width: 100%;
		height: 350rpx;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
	}

	.intro-text {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.intro-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 20rpx;
	}

	.intro-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}

	.intro-section {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #8b5a2b;
		margin-bottom: 20rpx;
		position: relative;
		text-align: center;
		padding: 0 30rpx;
		padding-bottom: 15rpx;
	}

	.section-title::before,
	.section-title::after {
		content: '';
		position: absolute;
		top: 50%;
		width: 60rpx;
		height: 1rpx;
		background-color: #8b5a2b;
	}

	.section-title::before {
		left: 50%;
		margin-left: -140rpx;
	}

	.section-title::after {
		right: 50%;
		margin-right: -140rpx;
	}

	.advantage-list {
		margin-top: 30rpx;
	}

	.advantage-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.advantage-icon {
		width: 80rpx;
		height: 80rpx;
		background-color: #faf5f0;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
		margin-right: 20rpx;
	}

	.advantage-text {
		font-size: 28rpx;
		color: #666;
	}

	.env-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 30rpx;
	}

	.env-item {
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.08);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		cursor: pointer;
	}

	.env-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	}

	.env-image {
		width: 100%;
		height: 400rpx;
		object-fit: cover;
	}

	.env-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #8b5a2b;
		text-align: center;
		padding: 20rpx 15rpx 10rpx;
	}
</style>