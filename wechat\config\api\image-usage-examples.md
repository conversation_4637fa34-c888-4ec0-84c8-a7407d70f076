# 图片API使用说明

## 概述

图片API封装提供了统一的图片处理功能，包括URL生成、图片预览、批量处理等功能。

## 导入方式

```javascript
// 导入整个API对象
import imageApi from '@/config/api/image.js';

// 或者按需导入特定功能
import {
  getImageUrl,
  generateImageList,
  previewImage,
  IMAGE_SIZES
} from '@/config/api/image.js';
```

## 基础用法

### 1. 获取单张图片URL

```javascript
// 获取不同尺寸的图片URL
const imageId = '1927001407836155905';

const smallUrl = imageApi.getSmallImageUrl(imageId);
const mediumUrl = imageApi.getMediumImageUrl(imageId);
const largeUrl = imageApi.getLargeImageUrl(imageId);

// 或者使用通用方法
const customUrl = imageApi.getImageUrl(imageId, 'medium');
```

### 2. 批量生成图片列表

```javascript
// 图片ID数组
const imageIds = [
  '1927001407836155905',
  '1927001948804902914',
  '1927002067306573825'
];

// 图片名称数组（可选）
const imageNames = ['护理室', '宝宝房', '专业设施'];

// 生成图片列表
const imageList = imageApi.generateImageList(imageIds, imageNames);

// 结果格式：
// [
//   {
//     id: '1927001407836155905',
//     name: '护理室',
//     smallImage: 'http://..../small',
//     mediumImage: 'http://..../medium',
//     largeImage: 'http://..../large'
//   },
//   ...
// ]
```

### 3. 图片预览功能

```javascript
// 简单预览
imageApi.previewImage(imageUrl);

// 预览大图
imageApi.previewImage(smallImageUrl, largeImageUrl);

// 预览图片列表中的某一张（支持左右滑动）
imageApi.previewImageFromList(imageList, currentIndex);
```

## 在Vue组件中的使用示例

### 示例1：简单图片展示

```vue
<template>
  <view class="image-gallery">
    <image
      v-for="(item, index) in imageList"
      :key="index"
      :src="item.smallImage"
      @click="previewImage(item.smallImage, item.largeImage)"
      class="gallery-image"
    />
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import imageApi from '@/config/api/image.js';

const imageList = ref([]);

// 图片ID列表
const imageIds = ['id1', 'id2', 'id3'];
const imageNames = ['图片1', '图片2', '图片3'];

// 图片预览
const previewImage = (currentImage, largeImage) => {
  imageApi.previewImage(currentImage, largeImage);
};

onMounted(() => {
  // 生成图片列表
  imageList.value = imageApi.generateImageList(imageIds, imageNames);
});
</script>
```

### 示例2：带加载状态的图片

```vue
<template>
  <view class="image-container">
    <image
      :src="imageUrl"
      @load="onImageLoad"
      @error="onImageError"
      v-if="!loading"
    />
    <view v-else class="loading">加载中...</view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import imageApi from '@/config/api/image.js';

const props = defineProps({
  imageId: String
});

const loading = ref(true);
const imageUrl = computed(() =>
  props.imageId ? imageApi.getMediumImageUrl(props.imageId) : ''
);

const onImageLoad = () => {
  loading.value = false;
};

const onImageError = () => {
  loading.value = false;
  uni.showToast({
    title: '图片加载失败',
    icon: 'none'
  });
};

onMounted(async () => {
  if (props.imageId) {
    // 检查图片是否可以加载
    const canLoad = await imageApi.checkImageLoad(imageUrl.value);
    if (!canLoad) {
      loading.value = false;
    }
  }
});
</script>
```

### 示例3：图片网格展示

```vue
<template>
  <view class="image-grid">
    <view
      v-for="(item, index) in imageList"
      :key="index"
      class="grid-item"
      @click="previewFromList(index)"
    >
      <image
        :src="item.smallImage"
        class="grid-image"
        mode="aspectFill"
      />
      <text class="image-name">{{ item.name }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import imageApi from '@/config/api/image.js';

const imageList = ref([]);

// 预览图片列表
const previewFromList = (index) => {
  imageApi.previewImageFromList(imageList.value, index);
};

// 初始化图片列表
const initImageList = (imageIds, names) => {
  imageList.value = imageApi.generateImageList(imageIds, names);
};

defineExpose({
  initImageList
});
</script>
```

## 高级功能

### 获取图片信息

```javascript
// 获取图片详细信息
const imageInfo = await imageApi.getImageInfo(imageUrl);
console.log('图片尺寸:', imageInfo.width, 'x', imageInfo.height);
```

### 检查图片加载状态

```javascript
// 检查图片是否可以正常加载
const canLoad = await imageApi.checkImageLoad(imageUrl);
if (!canLoad) {
  // 处理加载失败的情况
}
```

## 配置说明

图片API的基础配置在 `/config/api.js` 中：

```javascript
// 图片服务的固定地址
const IMAGE_SERVER_URL = 'http://************:8089';

// 图片相关API配置 - 使用固定的图片服务器地址
images: {
  baseUrl: `${IMAGE_SERVER_URL}${API_PATHS.images}`,
  getImageUrl: (imageId, size = 'medium') => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/${size}`,
  small: (imageId) => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/small`,
  medium: (imageId) => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/medium`,
  large: (imageId) => `${IMAGE_SERVER_URL}${API_PATHS.images}/${imageId}/large`
}
```

### 环境配置

所有环境（开发、测试、生产）都使用固定的后端服务地址：

```javascript
const BASE_URLS = {
  development: 'http://************:8089', // 本地开发环境
  test: 'http://************:8089',        // 测试环境
  production: 'http://************:8089'   // 生产环境
};
```

## 注意事项

1. **图片ID验证**：使用前请确保图片ID有效
2. **网络状态**：在网络不佳时，建议先加载小图再加载大图
3. **内存管理**：大量图片时注意内存使用，可以使用懒加载
4. **错误处理**：建议为图片加载添加错误处理逻辑

## 更新日志

- v1.0.0: 初始版本，包含基础图片URL生成和预览功能
- 支持小、中、大三种尺寸
- 支持批量图片处理
- 支持图片预览和信息获取
