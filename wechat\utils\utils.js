export function navigateTo(url) {
	if (!url || typeof url !== 'string') {
		uni.showToast({
			title: '无效的页面路径',
			icon: 'none'
		});
		return;
	}
	const pages = getCurrentPages();
	if (pages.length >= 10) {
		uni.showToast({
			title: '页面栈已达上限，请使用返回或关闭页面',
			icon: 'none'
		});
		return;
	}
	uni.navigateTo({
		url,
		fail: (err) => {
			let errorMessage = '页面跳转失败';
			if (err.errMsg) {
				errorMessage += `：${err.errMsg}`;
			}
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			});
		}
	});
}


export function showToast(title,icon = 'none') {
	uni.showToast({title,icon});
}

/**
 * 分享给朋友功能配置
 * @param {Object} options 分享配置选项
 * @param {string} options.title 分享标题
 * @param {string} options.path 分享页面路径
 * @param {string} options.imageUrl 分享图片URL
 * @returns {Object} 分享配置对象
 */
export function getShareAppMessageConfig(options = {}) {
	const defaultConfig = {
		title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务',
		path: '/pages/index/index',
	};

	return {
		...defaultConfig,
		...options
	};
}

/**
 * 分享到朋友圈功能配置
 * @param {Object} options 分享配置选项
 * @param {string} options.title 分享标题
 * @param {string} options.query 分享页面参数
 * @param {string} options.imageUrl 分享图片URL
 * @returns {Object} 分享配置对象
 */
export function getShareTimelineConfig(options = {}) {
	const defaultConfig = {
		title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
		query: '',
	};

	return {
		...defaultConfig,
		...options
	};
}