<template>
	<view class="order-container">
		<view class="order-header">
			<text class="header-title">我的订单</text>
		</view>

		<!-- 订单状态选项卡 -->
		<view class="order-tabs">
			<view
				class="tab-item"
				v-for="(tab, index) in tabs"
				:key="index"
				:class="{active: currentTab === index}"
				@click="switchTab(index)"
			>
				{{tab}}
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list" v-if="orderList.length > 0">
			<view class="order-item" v-for="(item, index) in orderList" :key="index">
				<view class="order-top">
					<text class="order-id">订单号：{{item.orderId}}</text>
					<text class="order-status" :class="item.statusClass">{{item.statusText}}</text>
				</view>
				<view class="order-content">
					<view class="order-info">
						<view class="info-item">
							<text class="item-label">服务类型：</text>
							<text class="item-value">{{item.serviceName}}</text>
						</view>
						<view class="info-item">
							<text class="item-label">下单时间：</text>
							<text class="item-value">{{item.createTime}}</text>
						</view>
						<view class="info-item">
							<text class="item-label">订单金额：</text>
							<text class="item-value price">¥{{item.price}}</text>
						</view>
					</view>
				</view>
				<view class="order-bottom">
					<button class="btn btn-default" v-if="item.status === 1">取消订单</button>
					<button class="btn btn-primary" v-if="item.status === 1">去支付</button>
					<button class="btn btn-default" v-if="item.status === 2">查看详情</button>
					<button class="btn btn-primary" v-if="item.status === 3">评价</button>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<image src="/static/empty.png" mode="aspectFit" class="empty-image"></image>
			<text class="empty-text">暂无相关订单</text>
		</view>
	</view>
</template>

<script>
/**
 * 订单列表页面
 * 用于展示用户的订单信息
 */
export default {
	data() {
		return {
			// 选项卡配置
			tabs: ['全部', '待付款', '待服务', '已完成', '已取消'],
			currentTab: 0,
			// 订单列表数据
			orderList: []
		}
	},
	// 页面加载生命周期
	onLoad() {
		this.loadOrderList();
	},
	methods: {
		/**
		 * 切换选项卡
		 * @param {Number} index 选项卡索引
		 */
		switchTab(index) {
			if (this.currentTab === index) return;
			this.currentTab = index;
			this.loadOrderList();
		},
		/**
		 * 加载订单列表
		 * 根据当前选项卡状态加载对应订单
		 */
		loadOrderList() {
			// 显示加载中
			wx.showLoading({
				title: '加载中...'
			});
			// 模拟获取订单数据
			setTimeout(() => {
				const mockOrders = [
					{
						orderId: 'DD20230001',
						status: 1, // 1-待付款 2-待服务 3-已完成 4-已取消
						statusText: '待付款',
						statusClass: 'status-pending',
						serviceName: '月子中心标准房',
						createTime: '2023-06-01 14:30',
						price: '15800.00'
					},
					{
						orderId: 'DD20230002',
						status: 2,
						statusText: '待服务',
						statusClass: 'status-processing',
						serviceName: '月子中心VIP房',
						createTime: '2023-05-28 10:15',
						price: '25800.00'
					},
					{
						orderId: 'DD20230003',
						status: 3,
						statusText: '已完成',
						statusClass: 'status-completed',
						serviceName: '产后修复套餐',
						createTime: '2023-04-15 09:30',
						price: '3680.00'
					},
					{
						orderId: 'DD20230004',
						status: 4,
						statusText: '已取消',
						statusClass: 'status-canceled',
						serviceName: '月子餐15天',
						createTime: '2023-03-20 16:45',
						price: '4500.00'
					}
				];
				if (this.currentTab === 0) {
					this.orderList = mockOrders;
				} else {
					this.orderList = mockOrders.filter(order => order.status === this.currentTab);
				}
				wx.hideLoading();
			}, 500);
		},
		/**
		 * 页面导航
		 * @param {String} url 目标页面路径
		 */
		navigateTo(url) {
			wx.navigateTo({
				url: url
			});
		}
	},
	// 分享给朋友
	onShareAppMessage() {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 专业月子护理服务',
			path: '/pages/index/index' // 跳转到首页
		});
	},
	// 分享到朋友圈
	onShareTimeline() {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护'
		});
	}
}
</script>

<style>
	.order-container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.order-header {
		margin-bottom: 30rpx;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	/* 选项卡样式 */
	.order-tabs {
		display: flex;
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 20rpx 0;
		font-size: 28rpx;
		color: #666;
		position: relative;
	}

	.tab-item.active {
		color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		font-weight: bold;
	}

	.tab-item.active:after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		border-radius: 2rpx;
	}

	/* 订单列表样式 */
	.order-list {
		margin-bottom: 30rpx;
	}

	.order-item {
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.order-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.order-id {
		font-size: 26rpx;
		color: #666;
	}

	.order-status {
		font-size: 26rpx;
		font-weight: bold;
	}

	.status-pending {
		color: #FF9900;
	}

	.status-processing {
		color: #3399FF;
	}

	.status-completed {
		color: #33CC99;
	}

	.status-canceled {
		color: #999;
	}

	.order-content {
		padding: 20rpx 30rpx;
	}

	.info-item {
		display: flex;
		margin-bottom: 10rpx;
	}

	.item-label {
		color: #999;
		font-size: 26rpx;
		width: 160rpx;
	}

	.item-value {
		color: #333;
		font-size: 26rpx;
		flex: 1;
	}

	.price {
		color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		font-weight: bold;
	}

	.order-bottom {
		display: flex;
		justify-content: flex-end;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #f5f5f5;
	}

	.btn {
		margin-left: 20rpx;
		font-size: 26rpx;
		padding: 10rpx 30rpx;
		border-radius: 50rpx;
		line-height: 1.5;
	}

	.btn-default {
		background-color: #fff;
		color: #666;
		border: 1rpx solid #ddd;
	}

	.btn-primary {
		background-color: #8b5a2b; /* 深棕色，与首页标题颜色一致 */
		color: #fff;
	}

	/* 空状态样式 */
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}

	.empty-text {
		color: #999;
		font-size: 28rpx;
	}
</style>