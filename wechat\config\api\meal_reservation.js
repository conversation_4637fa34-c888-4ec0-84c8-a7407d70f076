import request from '@/utils/request'

// 开发环境标志
const isDev = process.env.NODE_ENV === 'development';

// 获取餐食预约信息
export const getMealReservationInfoById = (params) => {
    return request({
        url: `/api/mealReservation/get`,
        method: 'post',
        data: params,
        useMock: isDev // 开发环境下使用模拟数据
    })
}

// 通过openId获取餐食预约信息
export function getMealReservationInfo() {
  return request({
    url: `/api/mealReservation/list`,
    method: 'post',
    useMock: isDev // 开发环境下使用模拟数据
  })
}


// 创建餐食预约信息
export const addMealReservationInfo = (params) => {
    return request({
        url: `/api/mealReservation/save`,
        method: 'post',
        data: params,
        useMock: isDev // 开发环境下使用模拟数据
    })
}

// 更新餐食预约信息
export function updateMealReservationInfo(params) {
  return request({
    url: `/api/mealReservation/update`,
    method: 'post',
    data: params,
    useMock: isDev // 开发环境下使用模拟数据
  })
}

//删除餐食预约信息
export const deleteMealReservationInfo = (params) => {
    return request({
        url: `/api/mealReservation/remove`,
        method: 'post',
        data: params,
        useMock: isDev // 开发环境下使用模拟数据
    })
}


// 通过openId获取当天餐食预约信息
export function getTodayMealReservationInfo() {
  return request({
    url: `/api/mealReservation/today`,
    method: 'post',
    useMock: isDev // 开发环境下使用模拟数据
  })
}
