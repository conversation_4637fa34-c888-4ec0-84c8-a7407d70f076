<template>
  <view class="container">
    <view class="header">
      <text class="title">孕妇信息登记</text>
      <text class="subtitle">请填写完整的个人信息</text>
    </view>

    <view class="form">
      <!-- 姓名 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>姓名</text>
        </view>
        <input 
          class="input" 
          v-model="formData.name" 
          placeholder="请输入您的姓名"
          maxlength="20"
        />
      </view>

      <!-- 电话 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>联系电话</text>
        </view>
        <input 
          class="input" 
          v-model="formData.phone" 
          placeholder="请输入手机号码"
          type="number"
          maxlength="11"
        />
      </view>

      <!-- 预产期 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>预产期</text>
        </view>
        <picker 
          mode="date" 
          :value="formData.dueDate" 
          @change="onDueDateChange"
          :start="minDate"
          :end="maxDate"
        >
          <view class="picker-input">
            <text :class="formData.dueDate ? 'picker-text' : 'picker-placeholder'">
              {{ formData.dueDate || '请选择预产期' }}
            </text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 体重 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>当前体重</text>
        </view>
        <view class="weight-input">
          <input 
            class="input weight-number" 
            v-model="formData.weight" 
            placeholder="请输入体重"
            type="digit"
          />
          <text class="weight-unit">kg</text>
        </view>
      </view>

      <!-- 产检医院 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>产检医院</text>
        </view>
        <picker 
          :range="hospitalOptions" 
          :value="hospitalIndex" 
          @change="onHospitalChange"
        >
          <view class="picker-input">
            <text :class="formData.hospital ? 'picker-text' : 'picker-placeholder'">
              {{ formData.hospital || '请选择产检医院' }}
            </text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 其他医院输入框 -->
      <view class="form-item" v-if="showOtherHospital">
        <view class="label">
          <text class="required">*</text>
          <text>请输入医院名称</text>
        </view>
        <input 
          class="input" 
          v-model="formData.otherHospital" 
          placeholder="请输入具体的医院名称"
          maxlength="50"
        />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @tap="handleSubmit">
        提交信息
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 表单数据
const formData = ref({
  name: '',
  phone: '',
  dueDate: '',
  weight: '',
  hospital: '',
  otherHospital: ''
})

// 医院选项
const hospitalOptions = ref([
  '第一妇幼',
  '第二妇幼', 
  '中心医院',
  '第三人民医院',
  '其他'
])

// 当前选中的医院索引
const hospitalIndex = ref(-1)

// 是否显示其他医院输入框
const showOtherHospital = computed(() => {
  return formData.value.hospital === '其他'
})

// 日期范围设置
const today = new Date()
const minDate = ref(today.toISOString().split('T')[0])
const maxDate = ref(new Date(today.getFullYear() + 1, today.getMonth(), today.getDate()).toISOString().split('T')[0])

// 预产期选择
const onDueDateChange = (e) => {
  formData.value.dueDate = e.detail.value
}

// 医院选择
const onHospitalChange = (e) => {
  const index = e.detail.value
  hospitalIndex.value = index
  formData.value.hospital = hospitalOptions.value[index]
  
  // 如果不是选择"其他"，清空其他医院输入
  if (formData.value.hospital !== '其他') {
    formData.value.otherHospital = ''
  }
}

// 表单验证
const validateForm = () => {
  if (!formData.value.name.trim()) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.phone.trim()) {
    uni.showToast({
      title: '请输入联系电话',
      icon: 'none'
    })
    return false
  }

  // 手机号格式验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(formData.value.phone)) {
    uni.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.dueDate) {
    uni.showToast({
      title: '请选择预产期',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.weight.trim()) {
    uni.showToast({
      title: '请输入体重',
      icon: 'none'
    })
    return false
  }

  // 体重范围验证
  const weight = parseFloat(formData.value.weight)
  if (isNaN(weight) || weight < 30 || weight > 200) {
    uni.showToast({
      title: '请输入合理的体重范围(30-200kg)',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.hospital) {
    uni.showToast({
      title: '请选择产检医院',
      icon: 'none'
    })
    return false
  }

  if (formData.value.hospital === '其他' && !formData.value.otherHospital.trim()) {
    uni.showToast({
      title: '请输入具体的医院名称',
      icon: 'none'
    })
    return false
  }

  return true
}

// 提交表单
const handleSubmit = () => {
  if (!validateForm()) {
    return
  }

  // 构建提交数据
  const submitData = {
    name: formData.value.name.trim(),
    phone: formData.value.phone.trim(),
    dueDate: formData.value.dueDate,
    weight: parseFloat(formData.value.weight),
    hospital: formData.value.hospital === '其他' ? formData.value.otherHospital.trim() : formData.value.hospital
  }

  console.log('提交数据:', submitData)

  // 显示提交成功提示
  uni.showToast({
    title: '信息提交成功',
    icon: 'success'
  })

  // 这里可以调用API提交数据
  // submitMaternalInfo(submitData)

  // 延迟返回上一页或跳转到其他页面
  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #f0f8ff 100%);
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #7f8c8d;
  }
}

.form {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
  
  .required {
    color: #e74c3c;
    margin-right: 8rpx;
    font-size: 32rpx;
  }
}

.input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #2c3e50;
  background: #fafafa;
  box-sizing: border-box;
  
  &:focus {
    border-color: #3498db;
    background: #ffffff;
  }
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fafafa;
  box-sizing: border-box;
  
  .picker-text {
    font-size: 30rpx;
    color: #2c3e50;
  }
  
  .picker-placeholder {
    font-size: 30rpx;
    color: #bdc3c7;
  }
  
  .picker-arrow {
    font-size: 24rpx;
    color: #bdc3c7;
    transform: rotate(90deg);
  }
}

.weight-input {
  display: flex;
  align-items: center;
  
  .weight-number {
    flex: 1;
    margin-right: 20rpx;
  }
  
  .weight-unit {
    font-size: 30rpx;
    color: #7f8c8d;
    font-weight: 500;
  }
}

.submit-section {
  margin-top: 60rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 6rpx 15rpx rgba(52, 152, 219, 0.3);
  }
}
</style>
