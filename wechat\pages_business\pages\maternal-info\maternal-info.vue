<template>
  <view class="container">
    <view class="header">
      <text class="title"></text>
      <text class="subtitle">请填写完整的个人信息</text>
    </view>

    <view class="form">
      <!-- 姓名 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>姓名</text>
        </view>
        <input 
          class="input" 
          v-model="formData.name" 
          placeholder="请输入您的姓名"
          maxlength="20"
        />
      </view>

      <!-- 电话 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>联系电话</text>
        </view>
        <input
          class="input"
          v-model="formData.phoneNumber"
          placeholder="请输入手机号码"
          type="number"
          maxlength="11"
        />
      </view>

      <!-- 预产期 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>预产期</text>
        </view>
        <picker 
          mode="date" 
          :value="formData.dueDate" 
          @change="onDueDateChange"
          :start="minDate"
          :end="maxDate"
        >
          <view class="picker-input">
            <text :class="formData.dueDate ? 'picker-text' : 'picker-placeholder'">
              {{ formData.dueDate || '请选择预产期' }}
            </text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 体重 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>当前体重</text>
        </view>
        <view class="weight-input">
          <input 
            class="input weight-number" 
            v-model="formData.weight" 
            placeholder="请输入体重"
            type="digit"
          />
          <text class="weight-unit">kg</text>
        </view>
      </view>

      <!-- 产检医院 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>产检医院</text>
        </view>
        <picker 
          :range="hospitalOptions" 
          :value="hospitalIndex" 
          @change="onHospitalChange"
        >
          <view class="picker-input">
            <text :class="formData.hospital ? 'picker-text' : 'picker-placeholder'">
              {{ formData.hospital || '请选择产检医院' }}
            </text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 其他医院输入框 -->
      <view class="form-item" v-if="showOtherHospital">
        <view class="label">
          <text class="required">*</text>
          <text>请输入医院名称</text>
        </view>
        <input
          class="input"
          v-model="formData.otherHospital"
          placeholder="请输入具体的医院名称"
          maxlength="50"
        />
      </view>

      <!-- 推荐码 -->
      <view class="form-item">
        <view class="label">
          <text class="required">*</text>
          <text>推荐码</text>
        </view>
        <input
          class="input"
          v-model="formData.referralCode"
          placeholder="请输入推荐码"
          maxlength="20"
        />
        <view class="field-tip">
          <text>请填写推荐码享受优惠</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @tap="handleSubmit">
        提交信息
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getLuckyCode } from "@/config/api/maternal-info.js";
import { updateUserInfo } from "@/config/api/user.js";

// 表单数据
const formData = ref({
  name: '',
  phoneNumber: '',
  dueDate: '',
  weight: '',
  hospital: '',
  otherHospital: '',
  referralCode: ''
})

// 医院选项
const hospitalOptions = ref([
  '第一妇幼',
  '第二妇幼', 
  '中心医院',
  '第三人民医院',
  '其他'
])

// 当前选中的医院索引
const hospitalIndex = ref(-1)

// 是否显示其他医院输入框
const showOtherHospital = computed(() => {
  return formData.value.hospital === '其他'
})

// 日期范围设置
const today = new Date()
const minDate = ref(today.toISOString().split('T')[0])
const maxDate = ref(new Date(today.getFullYear() + 1, today.getMonth(), today.getDate()).toISOString().split('T')[0])

// 预产期选择
const onDueDateChange = (e) => {
  formData.value.dueDate = e.detail.value
}

// 医院选择
const onHospitalChange = (e) => {
  const index = e.detail.value
  hospitalIndex.value = index
  formData.value.hospital = hospitalOptions.value[index]

  // 如果不是选择"其他"，清空其他医院输入
  if (formData.value.hospital !== '其他') {
    formData.value.otherHospital = ''
  }
}



// 表单验证
const validateForm = () => {
  if (!formData.value.name.trim()) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.phoneNumber.trim()) {
    uni.showToast({
      title: '请输入联系电话',
      icon: 'none'
    })
    return false
  }

  // 手机号格式验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(formData.value.phoneNumber)) {
    uni.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.dueDate) {
    uni.showToast({
      title: '请选择预产期',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.weight.trim()) {
    uni.showToast({
      title: '请输入体重',
      icon: 'none'
    })
    return false
  }

  // 体重范围验证
  const weight = parseFloat(formData.value.weight)
  if (isNaN(weight) || weight < 30 || weight > 200) {
    uni.showToast({
      title: '请输入合理的体重范围(30-200kg)',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.hospital) {
    uni.showToast({
      title: '请选择产检医院',
      icon: 'none'
    })
    return false
  }

  if (formData.value.hospital === '其他' && !formData.value.otherHospital.trim()) {
    uni.showToast({
      title: '请输入具体的医院名称',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.referralCode.trim()) {
    uni.showToast({
      title: '请输入推荐码',
      icon: 'none'
    })
    return false
  }

  return true
}

// 提交表单
const handleSubmit = async () => {
  // 第一步：校验信息是否填写完整
  if (!validateForm()) {
    // validateForm函数内部已经有相应的提示，这里直接返回
    return
  }

  // 构建提交数据
  const submitData = {
    name: formData.value.name.trim(),
    phoneNumber: formData.value.phoneNumber.trim(),
    dueDate: formData.value.dueDate,
    weight: parseFloat(formData.value.weight),
    hospital: formData.value.hospital === '其他' ? formData.value.otherHospital.trim() : formData.value.hospital,
    referralCode: formData.value.referralCode.trim()
  }

  console.log('提交数据:', submitData)

  try {
    // 第二步：获取最新的推荐码进行验证
    uni.showLoading({
      title: '正在验证推荐码...'
    })

    console.log('开始获取最新推荐码进行验证...')
    const luckyResponse = await getLuckyCode()
    console.log('获取推荐码结果:', luckyResponse)

    // 第三步：验证用户填写的推荐码是否正确
    if (luckyResponse) {
      const correctReferralCode = luckyResponse
      const userInputCode = submitData.referralCode

      console.log('正确的推荐码:', correctReferralCode)
      console.log('用户输入的推荐码:', userInputCode)
      console.log(typeof correctReferralCode)
      console.log(typeof userInputCode)

      // 比较推荐码是否一致
      if (Number(userInputCode) !== Number(correctReferralCode)) {
        // 推荐码不正确，提示用户
        uni.hideLoading()
        uni.showModal({
          title: '推荐码错误',
          content: '您输入的推荐码不正确，请检查后重新输入。',
          showCancel: false,
          confirmText: '确定'
        })
        return
      }

      // 推荐码正确，继续处理
      console.log('推荐码验证通过')

    } else {
      // 无法获取正确的推荐码
      uni.hideLoading()
      uni.showModal({
        title: '验证失败',
        content: '无法验证推荐码，请稍后重试。',
        showCancel: false,
        confirmText: '确定'
      })
      return
    }

    // 第四步：推荐码验证通过，调用updateUserInfo接口
    uni.showLoading({
      title: '正在提交信息...'
    })

    console.log('推荐码验证通过，开始提交用户信息...')
    const userResponse = await updateUserInfo(submitData)
    console.log('用户信息提交结果:', userResponse)

    // 隐藏加载提示
    uni.hideLoading()

    // 提交成功，弹出抽奖选择弹窗
    uni.showModal({
      title: '提交成功',
      content: '您的信息已成功提交！是否立即参与抽奖？',
      showCancel: true,
      cancelText: '晚点抽奖',
      confirmText: '前往抽奖',
      success: (res) => {
        if (res.confirm) {
          // 用户选择"前往抽奖"，跳转到转盘页面
          console.log('用户选择前往抽奖')
          uni.navigateTo({
            url: '/pages_business/pages/turntable/turntable'
          })
        } else if (res.cancel) {
          // 用户选择"晚点抽奖"，回到首页
          console.log('用户选择晚点抽奖')
          uni.switchTab({
            url: '/pages/index/index'
          })
        }
      }
    })

  } catch (error) {
    // 隐藏加载提示
    uni.hideLoading()

    console.error('操作失败:', error)

    // 显示错误提示
    uni.showModal({
      title: '操作失败',
      content: error.message || '操作失败，请重试',
      showCancel: false,
      confirmText: '确定'
    })
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #f0f8ff 100%);
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #7f8c8d;
  }
}

.form {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;

  .required {
    color: #e74c3c;
    margin-right: 8rpx;
    font-size: 32rpx;
  }

  .optional {
    color: #95a5a6;
    font-size: 24rpx;
    margin-left: 8rpx;
    font-weight: normal;
  }
}

.input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #2c3e50;
  background: #fafafa;
  box-sizing: border-box;
  
  &:focus {
    border-color: #3498db;
    background: #ffffff;
  }
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fafafa;
  box-sizing: border-box;
  
  .picker-text {
    font-size: 30rpx;
    color: #2c3e50;
  }
  
  .picker-placeholder {
    font-size: 30rpx;
    color: #bdc3c7;
  }
  
  .picker-arrow {
    font-size: 24rpx;
    color: #bdc3c7;
    transform: rotate(90deg);
  }
}

.weight-input {
  display: flex;
  align-items: center;

  .weight-number {
    flex: 1;
    margin-right: 20rpx;
  }

  .weight-unit {
    font-size: 30rpx;
    color: #7f8c8d;
    font-weight: 500;
  }
}

.field-tip {
  margin-top: 12rpx;

  text {
    font-size: 24rpx;
    color: #95a5a6;
    line-height: 1.4;
  }
}



.submit-section {
  margin-top: 60rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 6rpx 15rpx rgba(52, 152, 219, 0.3);
  }
}
</style>
