import request from '@/utils/request'

// 获取用户忌口信息
export const getAllergyInfo = (params) => {
    return request({
        url: `/api/allergy/get`,
        method: 'post',
		data: params
    })
}

// 获取用户忌口列表
export const getAllergyList = (params) => {
    return request({
        url: `/api/allergy/list`,
        method: 'post',
        data: params
    })
}

 
// 创建或更新用户忌口信息
export const saveAllergyInfo = (params) => {
    return request({
        url: `/api/allergy/save`,
        method: 'post',
        data: params
    })
}

//删除用户接口信息
export const deleteAllergyInfo = (openId) => {
    return request({
        url: `/api/allergy/delete/${openId}`,
        method: 'post'
    })
}
