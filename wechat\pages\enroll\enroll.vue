<template>
	<view class="service-container">
		<view class="header">
			<text class="header-title">入院服务</text>
		</view>
		<view class="service-content">
			<view class="service-card">
				<view class="service-description">
					<text class="service-title">特需服务</text>
					<text class="service-text">我们提供专业的入院特需服务，为您解决入院过程中的各种问题</text>
				</view>
				<button class="service-btn" @click="makePhoneCall">立即咨询</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

	/**
	 * 入院服务页面
	 * 提供特需服务咨询电话
	 */
	export default {
		data() {
			return {
				// 咨询电话
				phoneNumber: '15018680245'
			}
		},
		methods: {
			/**
			 * 拨打电话方法
			 * 点击按钮时调用，直接拨打咨询电话
			 */
			makePhoneCall() {
				const token = uni.getStorageSync('token');
				if (!token) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/login/login'
						});
					}, 500);
					return;
				}
				uni.makePhoneCall({
					phoneNumber: this.phoneNumber,
					success: () => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.error('拨打电话失败', err);
						uni.showToast({
							title: '拨打电话失败，请手动拨打' + this.phoneNumber,
							icon: 'none',
							duration: 2000
						});
					}
				});
			}
		},
		// 分享给朋友
		onShareAppMessage() {
			return getShareAppMessageConfig({
				title: '东方爱堡月子会所 - 入院服务',
				path: '/pages/enroll/enroll'
			});
		},
		// 分享到朋友圈
		onShareTimeline() {
			return getShareTimelineConfig({
				title: '东方爱堡月子会所入院服务，专业贴心的月子护理等您体验'
			});
		}
	}
</script>

<style>
	.service-container {
		padding: 100rpx 20rpx 20rpx;
		background-color: #FAFAF5;
		min-height: calc(100vh - 120rpx);
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	/* 头部样式 */
	.header {
		padding: 20rpx 0;
		margin-bottom: 30rpx;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #8b5a2b;
		/* 与首页标题颜色一致 */
	}

	.service-content {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx 0;
	}

	.service-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 40rpx;
		width: 100%;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.service-description {
		text-align: center;
		margin-bottom: 50rpx;
	}

	.service-title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.service-text {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		display: block;
		padding: 0 20rpx;
	}

	.service-btn {
		background-color: #CEB684 !important;
		color: #fff !important;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: bold;
		width: 80%;
		height: 90rpx;
		line-height: 90rpx;
		margin-top: 20rpx;
		border: none !important;
		outline: none !important;
	}

	/* 确保按钮在各种状态下都保持正确的颜色 */
	.service-btn::after {
		border: none !important;
	}

	.service-btn:hover {
		background-color: #B8A373 !important;
		color: #fff !important;
	}

	.service-btn:active {
		background-color: #A69562 !important;
		color: #fff !important;
	}
</style>