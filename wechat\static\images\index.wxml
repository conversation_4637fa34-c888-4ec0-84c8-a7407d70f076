<!-- pages/shop/dial/index.wxml -->

<view class="container">
  <background src="https://cnpc-js-static.oss-cn-hangzhou.aliyuncs.com/-2134522617.png" height="{{ 1112 }}" />
  <button-side top="{{ 80 }}" bindtap="handleAward">我的奖品</button-side>
  <!-- 剩余次数 -->
  <view class="tip">剩余抽奖次数 <text>{{ count }}</text> 次</view>
  <!-- 大转盘 -->
  <view class="dial">
    <image class="arrow" src="/assets/background/pin_arrow.png"></image>
    <image class="bg" style="transform: rotate({{ degree }}deg);transition: transform {{ duration }}s" src="{{ dzpAwardInfo.background_image }}" />
    <image class="go" bindtap="handleTurn" src="/assets/background/img_dzp_go.png">
      <view class="info">
        <view class="title">GO</view>
        <view class="cost">{{ dzpAwardInfo.credit }}积分/次</view>
      </view>
    </image>
  </view>

  <!-- 奖品介绍 -->
  <view class="intro">
    <view class="title">
      <image src="/assets/icon/icon_rule_left.png" />奖品介绍
      <image src="/assets/icon/icon_rule_right.png" />
    </view>
    <scroll-view class="content" scroll-x="{{true}}">
      <view class="prize" wx:for="{{ prize }}" wx:key="id" wx:if="{{ item.type !== 0 }}">
        <image src="{{ item.cover || item.type === 3 ? '/assets/icon/award_coupon.png' : '/assets/icon/award_credit.png' }}" />
        <view class="level">{{ item.level }}</view>
        <view class="name">{{ item.name }}</view>
      </view>
    </scroll-view>
  </view>
  <!-- 中奖介绍 -->
  <view class="intro" style="height: 450rpx; margin-top:25rpx">
    <view class="title">
      <image src="/assets/icon/icon_rule_left.png" />中奖名单
      <image src="/assets/icon/icon_rule_right.png" />
    </view>
    <view class="table-view head">
      <view class="block" style="width: 20%;"> <text>奖品</text> </view>
      <view class="block border1" style="width: 30%;"> <text>手机号</text> </view>
      <view class="block" style="width: 50%;border: none;"> <text>中奖时间</text> </view>
    </view>
    <view class="table-content">
      <view class="scroll-list">
        <view wx:for="{{jackpotList}}" wx:key="index">
          <view class="table-view dec">
            <view class="block" style="width: 20%;"> <text>{{item.award.name}}</text> </view>
            <view class="block border1" style="width: 30%;"> <text>{{item.user.phone}}</text> </view>
            <view class="block" style="width: 50%;"> <text>{{item.created}}</text> </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 规则 -->
  <!-- <rule data="{{ rule }}" /> -->
  <view class="rule">
    <view class="title">
      <image src="/assets/icon/icon_rule_left.png" />规则
      <image src="/assets/icon/icon_rule_right.png" />
    </view>
    <view class="rule-content">
      <text>{{ dzpAwardInfo.intro }}</text>
    </view>
  </view>
</view>

<!-- 获取手机号弹窗 -->
<dialog-phone show="{{ showPhone }}" bind:successPhone="successPhone"></dialog-phone>

<!-- 判断是否有活动提示 -->
<van-dialog id="van-dialog" />

<!-- 获奖提示框 -->
<van-overlay show="{{ show }}">
  <view class="rule-wrap">
    <view class="rule-content">
      <view wx:if="{{ index < prize.length  }}" class="prize">
        <image src="{{ prize[index].cover || prize[index].type === 3 ? '/assets/icon/award_coupon.png' : '/assets/icon/award_credit.png' }}" />
        <view class="level">{{ prize[index].level }}</view>
      </view>
      <image class="image" wx:else src="/assets/icon/icon_sorry.png" />
      <view class="text">
        <view>{{ index < prize.length ? '恭喜您中了' + prize[index].name : '很遗憾，您没有中奖' }}</view>
            <view>{{ index < prize.length ? '请尽快领取' : '请再接再厉' }}</view>
            </view>
            <button-common wx:if="{{ index < prize.length  }}" custom-class="common light" bindtap="handleReceive" style="margin-bottom: -45rpx;">领取奖品</button-common>
            <button-common custom-class="common" bindtap="handleClose">确定</button-common>
        </view>
        <view class="rule-close" catchtap="handleClose">
          <van-icon name="cross" />
        </view>
      </view>
</van-overlay>