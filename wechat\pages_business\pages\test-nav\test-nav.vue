<template>
  <view class="container">
    <view class="header">
      <text class="title">页面导航测试</text>
    </view>
    
    <view class="nav-list">
      <view class="nav-item" @tap="goToMaternalInfo">
        <view class="nav-icon">👶</view>
        <view class="nav-content">
          <text class="nav-title">孕妇信息登记</text>
          <text class="nav-desc">填写姓名、电话、预产期等信息</text>
        </view>
        <text class="nav-arrow">></text>
      </view>
      
      <view class="nav-item" @tap="goToTurntable">
        <view class="nav-icon">🎯</view>
        <view class="nav-content">
          <text class="nav-title">抽奖大转盘</text>
          <text class="nav-desc">点击转盘进行抽奖</text>
        </view>
        <text class="nav-arrow">></text>
      </view>
    </view>
  </view>
</template>

<script setup>
// 跳转到孕妇信息页面
const goToMaternalInfo = () => {
  uni.navigateTo({
    url: '/pages_business/pages/maternal-info/maternal-info'
  })
}

// 跳转到转盘页面
const goToTurntable = () => {
  uni.navigateTo({
    url: '/pages_business/pages/turntable/turntable'
  })
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
  }
}

.nav-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f9fa;
  }
}

.nav-icon {
  font-size: 48rpx;
  margin-right: 30rpx;
}

.nav-content {
  flex: 1;
  
  .nav-title {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 10rpx;
  }
  
  .nav-desc {
    display: block;
    font-size: 26rpx;
    color: #7f8c8d;
  }
}

.nav-arrow {
  font-size: 32rpx;
  color: #bdc3c7;
  transform: rotate(90deg);
}
</style>
