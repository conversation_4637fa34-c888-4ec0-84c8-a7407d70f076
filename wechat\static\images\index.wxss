/* pages/shop/dial/index.wxss */

.container {
  background: #f8ae61;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
  position: relative;
}

.dial .bg {
  width: 620rpx;
  height: 620rpx;
}

.dial .arrow {
  position: absolute;
  top: -20rpx;
  left: 280rpx;
  width: 64rpx;
  height: 100rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  top: 230rpx;
  left: 230rpx;
  width: 160rpx;
  height: 160rpx;
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}

/* intro */

.intro {
  width: 700rpx;
  height: 340rpx;
  margin-top: 128rpx;
  padding: 25rpx;
  border-radius: 6px;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
}

.intro .title {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45rpx;
  margin: 20rpx 0;
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.intro .content {
  overflow: auto;
}

.intro .title image {
  width: 64rpx;
  height: 20rpx;
}

.intro .title image:first-child {
  margin-right: 20rpx;
}

.intro .title image:last-child {
  margin-left: 20rpx;
}

.intro .content {
  width: 700rpx;
  margin-left: -25rpx;
  white-space: nowrap;
}

/* prize */

.prize {
  position: relative;
  display: inline-block;
  width: 168rpx;
}

.prize {
  margin-left: 20rpx;
}

.prize:last-child {
  margin-right: 20rpx;
}

.prize image {
  width: 168rpx;
  height: 168rpx;
  background-color: #f3f3f3;
}

.prize .level {
  position: absolute;
  top: 128rpx;
  width: 168rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, .3);
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  line-height: 40rpx;
}

.prize .name {
  height: 34rpx;
  overflow: hidden;
  color: #333;
  font-size: 24rpx;
  text-align: center;
  text-overflow: ellipsis;
  line-height: 34rpx;
  white-space: nowrap;
}

/* 规则 */
.rule {
  width: 700rpx;
  /* height: 700rpx; */
  margin: 25rpx 0;
  padding: 25rpx;
  border-radius: 6px;
  background-color: #fff;
  box-sizing: border-box;
}

.rule .title {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45rpx;
  margin: 20rpx 0;
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.rule .title image {
  width: 64rpx;
  height: 20rpx;
}

.rule .title image:first-child {
  margin-right: 20rpx;
}

.rule .title image:last-child {
  margin-left: 20rpx;
}

.rule .item {
  margin: 32rpx 16rpx;
  color: #333;
  font-size: 24rpx;
}

.rule-content {
  color: #333;
  font-size: 24rpx;
}

/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}


/* 中奖介绍 */
.table-content {
  position: relative;
  height: 240rpx;
  width: 100%;
  margin: auto;
  overflow: hidden;
}

.table-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60rpx;
  width: 100%;
}

.head {
  font-size: 35rpx;
  font-weight: 500;
  color: #fff;
  background: #7b0c08;
}

.dec{
  font-size: 28rpx;
  color: #7b0c08;
}


.table-view .block {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  
}
.border1{
  border-left: 4rpx solid #c4644a !important;
  border-right: 4rpx solid #c4644a !important;
}

.table-content>.scroll-list {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: scroll 10s linear infinite normal;
}

.table-content>.scroll-list>view {
  width: 100%;
  height: 60rpx;
  background-color: #fde4cb;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.table-content>.scroll-list>view:nth-child(2n) {
  background-color: #f4cfac;
}

@keyframes scroll {
  100% {
    /* 需要滚动内容的总高度 */
    top: -480rpx;
  }
}
