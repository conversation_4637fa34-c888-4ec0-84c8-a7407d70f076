import request from '@/utils/request'

// 开发环境标志
const isDev = process.env.NODE_ENV === 'development';

// 获取车辆信息
export const getCarInfoById = (params) => {
    return request({
        url: `/api/cars/get`,
        method: 'post',
        data: params,
        useMock: isDev // 开发环境下使用模拟数据
    })
}

// 通过openId获取车辆信息列表
export function getCarInfoList() {
  return request({
    url: `/api/cars/list`,
    method: 'post',
    useMock: isDev // 开发环境下使用模拟数据
  })
}

// 创建或更新车辆信息
export const addOrUpdateCarInfo = (params) => {
    console.log('添加或更新车辆信息，参数:', params);
    return request({
        url: `/api/cars/save`,
        method: 'post',
        data: params,
        useMock: isDev // 开发环境下使用模拟数据
    })
}

// 删除车辆信息
export const deleteCarInfo = (params) => {
    console.log('删除车辆信息，参数:', params);
    return request({
        url: `/api/cars/remove`,
        method: 'post',
        data: params,
        useMock: isDev // 开发环境下使用模拟数据
    })
} 