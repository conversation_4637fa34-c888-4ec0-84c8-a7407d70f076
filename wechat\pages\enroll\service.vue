<template>
	<view class="service-container">
		<custom-nav-bar title="入住服务"></custom-nav-bar>

		<!-- 模块化入口 -->
		<view class="module-grid">
			

			<!-- 家属餐预约入口 -->
			<!-- <view class="module-item" @click="navigateTo('/pages_business/pages/enroll/meal')">
				<view class="module-icon meal-icon">
					<text class="iconfont">🍲</text>
				</view>
				<view class="module-info">
					<text class="module-title">陪护餐预约</text>
					<text class="module-desc">预约家属用餐时间和人数</text>
				</view>
				<view class="module-arrow">
					<up-icon name="arrow-right" color="#ccc" size="16"></up-icon>
				</view>
			</view> -->
			
			<!-- 我的家属餐预约入口 -->
			<view class="module-item" @click="navigateTo('/pages_business/pages/enroll/meal_list')">
				<view class="module-icon reservation-icon">
					<text class="iconfont">📋</text>
				</view>
				<view class="module-info">
					<text class="module-title">陪护餐预约</text>
					<text class="module-desc">查看已预约的陪护餐记录</text>
				</view>
				<view class="module-arrow">
					<up-icon name="arrow-right" color="#ccc" size="16"></up-icon>
				</view>
			</view>
			
			<!-- 用餐忌口信息入口 -->
			<view class="module-item" @click="navigateTo('/pages_business/pages/enroll/allergy_list')">
				<view class="module-icon dietary-icon">
					<text class="iconfont">🍽️</text>
				</view>
				<view class="module-info">
					<text class="module-title">用餐忌口</text>
					<text class="module-desc">查看饮食禁忌和过敏源</text>
				</view>
				<view class="module-arrow">
					<up-icon name="arrow-right" color="#ccc" size="16"></up-icon>
				</view>
			</view>

		</view>




	</view>
</template>

<script setup>
	import { listMemberCheckin } from "@/config/api/member_check_in.js";
	import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';
	const navigateTo = async (url) =>{
		const token = uni.getStorageSync('token');
		
		if (!token) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			}, 500);
			return;
		}
		
		// 获得该用户是不是已入住的会员
		try {
			const userInfoStr = JSON.parse(uni.getStorageSync('userInfo'));
		    const response = await listMemberCheckin({}); 
			if(typeof response === 'object'){
				uni.setStorageSync('memberInfo', JSON.stringify(response));
			}
			uni.navigateTo({
				url,
				fail: (err) => {
					console.error('导航失败:', err);
					uni.showToast({
						title: '页面打开失败',
						icon: 'none'
					});
				}
			});
		} catch (error) {
			uni.showToast({
				title: '页面打开失败',
				icon: 'none'
			});
		}
	}

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡月子会所 - 入住服务',
			path: '/pages/enroll/service'
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡月子会所入住服务，让您的月子生活更加舒心'
		});
	}
</script>

<style lang="scss" scoped>
	.service-container {
		padding: 0 20rpx 20rpx;
		background-color: #FAFAF5;
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}
	
	.module-grid {
		display: flex;
		flex-direction: column;
		padding-top: 30rpx;
		
		.module-item {
			display: flex;
			align-items: center;
			background-color: #fff;
			padding: 30rpx;
			border-radius: 16rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			
			.module-icon {
				width: 80rpx;
				height: 80rpx;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20rpx;
				
				.iconfont {
					font-size: 40rpx;
				}
				
				&.dietary-icon {
					background-color: #EBFAF2;
				}
				
				&.meal-icon {
					background-color: #FCF3E3;
				}
				
				&.reservation-icon {
					background-color: #E5F1FC;
				}
			}
			
			.module-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				
				.module-title {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
					margin-bottom: 8rpx;
				}
				
				.module-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.module-arrow {
				margin-left: 20rpx;
			}
		}
	}
</style>
